<template>
  <Teleport to="body">
    <div class="modal fade lumi-fade" :id="modalId" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
        <div class="modal-header border-0 pb-0">
          <div class="modal-title-container">
            <h5 class="modal-title">
              <i class="fas fa-rocket text-success me-2"></i>
              Teste Gratuito - Escolha seu Plano
            </h5>
            <p class="modal-subtitle mb-0">
              Selecione o plano que deseja testar gratuitamente
            </p>
          </div>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>

        <div class="modal-body">
          <!-- Loading -->
          <div v-if="isLoading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Carregando...</span>
            </div>
            <p class="mt-2 text-muted">Carregando planos disponíveis...</p>
          </div>

          <!-- Mensagem se não houver planos trial -->
          <div v-if="!isLoading && planosTrial.length === 0" class="text-center py-4">
            <i class="fas fa-info-circle text-muted mb-3" style="font-size: 3rem;"></i>
            <p class="text-muted">Nenhum plano de teste disponível no momento.</p>
          </div>

          <!-- Lista de Planos Trial -->
          <div v-if="!isLoading && planosTrial.length > 0" class="available-plans-section">
            <div class="plans-grid">
              <div 
                v-for="plano in planosTrial" 
                :key="plano.id"
                class="plan-card plan-card--trial"
                :class="{ 'plan-card--selected': selectedPlano?.id === plano.id }"
                @click="selectPlano(plano)"
              >
                <div class="plan-card-header" :style="{ backgroundColor: plano.cor }">
                  <div class="plan-icon">
                    <i :class="getPlanoIcon(plano)"></i>
                  </div>
                  <div class="trial-badge">
                    <span>{{ plano.dias_gratuitos || 30 }} DIAS GRÁTIS</span>
                  </div>
                </div>
                
                <div class="plan-card-body">
                  <div class="plan-header-info">
                    <h6 class="plan-name">{{ plano.nome }}</h6>
                    <div class="plan-price">
                      {{ formatCurrency(plano.valor_mensal) }}
                      <span class="price-period">/mês após o teste</span>
                    </div>
                  </div>

                  <div class="plan-features-grid">
                    <div class="features-column">
                      <div class="feature-item" v-if="plano.quantidade_usuarios">
                        <i class="fas fa-users"></i>
                        <span>{{ formatQuantity(plano.quantidade_usuarios) }} usuários</span>
                      </div>
                      <div class="feature-item" v-if="plano.quantidade_agendas">
                        <i class="fas fa-calendar"></i>
                        <span>{{ formatQuantity(plano.quantidade_agendas) }} agendas</span>
                      </div>
                    </div>
                    <div class="features-column">
                      <div class="feature-item" v-if="plano.modulo_ortodontia">
                        <i class="fas fa-tooth"></i>
                        <span>Ortodontia</span>
                      </div>
                      <div class="feature-item" v-if="plano.quantidade_mentorias_mensais">
                        <i class="fas fa-graduation-cap"></i>
                        <span>{{ formatQuantity(plano.quantidade_mentorias_mensais) }} mentorias</span>
                      </div>
                    </div>
                  </div>

                  <div class="plan-description" v-if="plano.observacoes">
                    <p class="text-muted small mb-0">{{ plano.observacoes }}</p>
                  </div>
                </div>
                
                <div class="plan-card-footer">
                  <div class="selection-indicator">
                    <i class="fas fa-check-circle"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer border-0 pt-0">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="fas fa-times me-2"></i>
            Cancelar
          </button>
          <button 
            type="button" 
            class="btn btn-success"
            :disabled="!selectedPlano"
            @click="confirmarPlanoSelecionado"
          >
            <i class="fas fa-arrow-right me-2"></i>
            Continuar
          </button>
        </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script>
import { planosService } from '@/services/planosService';
import cSwal from '@/utils/cSwal';

export default {
  name: 'TrialPlanSelectorModal',
  props: {
    modalId: {
      type: String,
      default: 'trialPlanSelectorModal'
    }
  },
  emits: ['plano-selecionado'],
  data() {
    return {
      isLoading: false,
      planos: [],
      selectedPlano: null,
    };
  },
  computed: {
    planosTrial() {
      if (!Array.isArray(this.planos)) {
        return [];
      }
      return this.planos.filter(plano => plano.trial_available && plano.ativo);
    }
  },
  mounted() {
    // Carregar planos quando o modal for montado
    const modalElement = document.getElementById(this.modalId);
    if (modalElement) {
      modalElement.addEventListener('shown.bs.modal', () => {
        if (this.planos.length === 0) {
          this.loadPlanosTrial();
        }
      });
    }
  },
  methods: {
    async loadPlanosTrial() {
      this.isLoading = true;
      try {
        const response = await planosService.getPlanos({ 
          ativo: true
        });
        this.planos = Array.isArray(response) ? response : (response?.data || []);
      } catch (error) {
        console.error('Erro ao carregar planos trial:', error);
        this.planos = [];
        cSwal.cError('Erro ao carregar planos disponíveis.');
      }
      this.isLoading = false;
    },
    
    selectPlano(plano) {
      this.selectedPlano = plano;
    },
    
    confirmarPlanoSelecionado() {
      if (this.selectedPlano) {
        this.$emit('plano-selecionado', this.selectedPlano);
      }
    },
    
    getPlanoIcon(plano) {
      if (plano.modulo_ortodontia) return 'fas fa-tooth';
      return 'fas fa-hospital-building';
    },
    
    formatCurrency(value) {
      return planosService.formatCurrency(value);
    },
    
    formatQuantity(value) {
      return planosService.formatQuantity(value);
    }
  }
};
</script>

<style scoped>
.modal-title-container {
  flex: 1;
}

.modal-subtitle {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: normal;
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.plan-card {
  border: 2px solid #e9ecef;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.plan-card--trial {
  border-color: #28a745;
  position: relative;
}

.plan-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.plan-card--selected {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.15);
  transform: translateY(-4px);
}

.plan-card-header {
  position: relative;
  padding: 1rem;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.plan-icon {
  font-size: 1.5rem;
}

.trial-badge {
  background: rgba(255, 255, 255, 0.95);
  color: #28a745;
  padding: 0.375rem 0.75rem;
  border-radius: 16px;
  font-size: 0.7rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.plan-card-body {
  padding: 1rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.plan-header-info {
  margin-bottom: 1rem;
}

.plan-name {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.plan-price {
  font-size: 1.25rem;
  font-weight: 700;
  color: #2d3748;
}

.price-period {
  font-size: 0.75rem;
  font-weight: 400;
  color: #718096;
}

.plan-features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.features-column {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #4a5568;
}

.feature-item i {
  width: 20px;
  color: #718096;
  flex-shrink: 0;
}

.plan-description {
  margin-top: auto;
  padding-top: 0.75rem;
  border-top: 1px solid #e9ecef;
}

.plan-card-footer {
  padding: 0.75rem 1rem;
  background: #f8f9fa;
  display: flex;
  justify-content: center;
}

.selection-indicator {
  color: #007bff;
  font-size: 1.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.plan-card--selected .selection-indicator {
  opacity: 1;
}

.btn {
  border-radius: 8px;
  font-weight: 500;
  padding: 0.625rem 1.25rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsividade */
@media (max-width: 768px) {
  .plans-grid {
    grid-template-columns: 1fr;
  }
  
  .plan-features-grid {
    grid-template-columns: 1fr;
  }
}
</style>
