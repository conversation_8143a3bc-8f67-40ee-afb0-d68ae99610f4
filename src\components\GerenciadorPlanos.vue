<template>
  <div class="gerenciador-planos">
    <!-- Header com título e botão de adicionar -->
    <div class="d-flex justify-content-between align-items-center mb-4">
      <div>
        <h4 class="mb-1">Gerenciar Planos</h4>
        <p class="text-muted mb-0">Configure os planos disponíveis para as clínicas</p>
      </div>
      <button 
        class="btn btn-primary btn-sm"
        @click="openModalNovoPlano"
        :disabled="isLoading"
      >
        <i class="fas fa-plus me-2"></i>
        Novo Plano
      </button>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="text-center py-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Carregando...</span>
      </div>
    </div>

    <!-- Empty state -->
    <div v-else-if="planos.length === 0" class="text-center py-5">
      <div class="empty-state">
        <i class="fas fa-layer-group fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">Nenhum plano cadastrado</h5>
        <p class="text-muted">Crie o primeiro plano para começar</p>
        <button class="btn btn-primary" @click="openModalNovoPlano">
          <i class="fas fa-plus me-2"></i>
          Criar Primeiro Plano
        </button>
      </div>
    </div>

    <!-- Grid de planos -->
    <div v-else class="planos-grid">
      <div 
        v-for="plano in planos" 
        :key="plano.id"
        class="plano-card"
        :class="{ 'plano-inativo': !plano.ativo }"
      >
        <!-- Header do card -->
        <div class="plano-header" :style="{ backgroundColor: plano.cor }">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <h5 class="text-white mb-1">{{ plano.nome }}</h5>
              <p class="text-white-50 mb-0 small">{{ plano.descricao || 'Sem descrição' }}</p>
            </div>
            <div class="dropdown-custom" :class="{ 'show': dropdownOpen === plano.id }">
              <button
                class="btn btn-sm btn-link text-white p-1"
                type="button"
                @click="toggleDropdown(plano.id)"
              >
                <i class="fas fa-ellipsis-v"></i>
              </button>
              <ul class="dropdown-menu-custom" v-show="dropdownOpen === plano.id">
                <li>
                  <a class="dropdown-item-custom" href="#" @click.prevent="editPlano(plano)">
                    <i class="fas fa-edit me-2"></i>Editar
                  </a>
                </li>
                <li>
                  <a class="dropdown-item-custom" href="#" @click.prevent="togglePlanoStatus(plano)">
                    <i class="fas me-2" :class="plano.ativo ? 'fa-eye-slash' : 'fa-eye'"></i>
                    {{ plano.ativo ? 'Inativar' : 'Ativar' }}
                  </a>
                </li>
                <li><hr class="dropdown-divider-custom"></li>
                <li>
                  <a class="dropdown-item-custom text-danger" href="#" @click.prevent="deletePlano(plano)">
                    <i class="fas fa-trash me-2"></i>Excluir
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Corpo do card compacto - BUGS CORRIGIDOS -->
        <div class="plano-body-compact">
          <!-- Linha 1: Valor e Status -->
          <div class="d-flex justify-content-between align-items-center mb-2">
            <div class="valor-compact">
              <strong class="fs-5">{{ formatCurrency(plano.valor_mensal) }}</strong>
              <small class="text-muted">/mês</small>
            </div>
            <div class="status-compact">
              <span class="badge" :class="plano.ativo ? 'bg-success' : 'bg-secondary'">
                {{ plano.ativo ? 'Ativo' : 'Inativo' }}
              </span>
            </div>
          </div>

          <!-- Linha 2: Módulos -->
          <div class="d-flex justify-content-start align-items-center mb-2">
            <span class="badge bg-success me-2">
              <i class="fas fa-clinic-medical me-1"></i>Clínica
            </span>
            <span v-if="plano.modulo_ortodontia" class="badge bg-primary">
              <i class="fas fa-teeth me-1"></i>Ortodontia
            </span>
          </div>

          <!-- Linha 3: Limites principais -->
          <div class="limites-compact">
            <div class="row g-2">
              <div class="col-6">
                <small class="text-muted d-block">Usuários</small>
                <strong>{{ formatQuantity(plano.quantidade_usuarios) }}</strong>
              </div>
              <div class="col-6">
                <small class="text-muted d-block">Agendas</small>
                <strong>{{ formatQuantity(plano.quantidade_agendas) }}</strong>
              </div>
              <div class="col-6" v-if="plano.modulo_ortodontia">
                <small class="text-muted d-block">
                  <i class="fas fa-graduation-cap me-1"></i>Mentorias
                </small>
                <strong>{{ formatQuantity(plano.quantidade_mentorias_mensais) }}</strong>
              </div>
              <div class="col-6">
                <small class="text-muted d-block">Fidelidade</small>
                <strong>{{ plano.meses_fidelidade_minima }} meses</strong>
              </div>
            </div>
          </div>

          <!-- Footer compacto -->
          <div class="plano-footer-compact mt-2 pt-2 border-top">
            <small class="text-muted">
              {{ plano.clinicas_count || 0 }} clínica(s) usando
            </small>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal para criar/editar plano - usando teleport para renderizar no body -->
    <teleport to="body">
      <PlanoModal
        :show="showModal"
        :plano="planoEditando"
        :is-editing="isEditing"
        @close="closeModal"
        @save="savePlano"
      />
    </teleport>
  </div>
</template>

<script>
import planosService from '@/services/planosService';
import cSwal from '@/utils/cSwal';
import PlanoModal from './PlanoModal.vue';

export default {
  name: 'GerenciadorPlanos',
  components: {
    PlanoModal
  },
  data() {
    return {
      planos: [],
      isLoading: false,
      showModal: false,
      planoEditando: null,
      isEditing: false,
      dropdownOpen: null
    };
  },
  async mounted() {
    await this.loadPlanos();
    // Fechar dropdown ao clicar fora
    document.addEventListener('click', this.closeDropdownOnClickOutside);
  },
  beforeUnmount() {
    document.removeEventListener('click', this.closeDropdownOnClickOutside);
  },
  methods: {
    async loadPlanos() {
      this.isLoading = true;
      try {
        const response = await planosService.getPlanos({ order_by: 'nome' });
        this.planos = response.data || [];
      } catch (error) {
        console.error('Erro ao carregar planos:', error);
        cSwal.cError('Erro ao carregar planos');
      } finally {
        this.isLoading = false;
      }
    },

    openModalNovoPlano() {
      this.planoEditando = planosService.getDefaultPlanoData();
      this.isEditing = false;
      this.showModal = true;
    },

    toggleDropdown(planoId) {
      this.dropdownOpen = this.dropdownOpen === planoId ? null : planoId;
    },

    closeDropdownOnClickOutside(event) {
      if (!event.target.closest('.dropdown-custom')) {
        this.dropdownOpen = null;
      }
    },

    editPlano(plano) {
      this.planoEditando = { ...plano };
      this.isEditing = true;
      this.showModal = true;
    },

    closeModal() {
      this.showModal = false;
      this.planoEditando = null;
      this.isEditing = false;
    },

    async savePlano(planoData) {
      try {
        if (this.isEditing) {
          await planosService.updatePlano(this.planoEditando.id, planoData);
          cSwal.cSuccess('Plano atualizado com sucesso!');
        } else {
          await planosService.createPlano(planoData);
          cSwal.cSuccess('Plano criado com sucesso!');
        }
        
        this.closeModal();
        await this.loadPlanos();
      } catch (error) {
        console.error('Erro ao salvar plano:', error);
        const message = error.response?.data?.message || 'Erro ao salvar plano';
        cSwal.cError(message);
      }
    },

    async togglePlanoStatus(plano) {
      const action = plano.ativo ? 'desativar' : 'ativar';
      
      cSwal.cConfirm(`Deseja ${action} o plano "${plano.nome}"?`, async () => {
        try {
          await planosService.toggleStatus(plano.id);
          cSwal.cSuccess(`Plano ${action === 'ativar' ? 'ativado' : 'desativado'} com sucesso!`);
          await this.loadPlanos();
        } catch (error) {
          console.error('Erro ao alterar status:', error);
          cSwal.cError('Erro ao alterar status do plano');
        }
      });
    },

    async deletePlano(plano) {
      cSwal.cConfirm(
        `Deseja excluir o plano "${plano.nome}"?`,
        async () => {
          try {
            await planosService.deletePlano(plano.id);
            cSwal.cSuccess('Plano excluído com sucesso!');
            await this.loadPlanos();
          } catch (error) {
            console.error('Erro ao excluir plano:', error);
            const message = error.response?.data?.message || 'Erro ao excluir plano';
            cSwal.cError(message);
          }
        },
        'Esta ação não pode ser desfeita!'
      );
    },

    formatCurrency(value) {
      return planosService.formatCurrency(value);
    },

    formatQuantity(value) {
      return planosService.formatQuantity(value);
    }
  }
};
</script>

<style scoped>
.gerenciador-planos {
  padding: 2rem;
  min-height: 100%;
  max-width: 1400px;
  margin: 0 auto;
}

.planos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.plano-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.plano-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.plano-inativo {
  opacity: 0.7;
}

.plano-header {
  padding: 1rem;
  position: relative;
}

.plano-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
  pointer-events: none;
}

.plano-body {
  padding: 1rem;
}

.valor-section {
  text-align: center;
  padding: 0.75rem 0;
  background: #f8f9fa;
  border-radius: 8px;
  margin: -0.25rem -0.25rem 0.75rem -0.25rem;
}

.valor-principal {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.valor-periodo {
  font-size: 0.875rem;
  color: #6c757d;
  margin-top: 0.25rem;
}

.modulos-section {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 0.5rem;
}

.modulo-item {
  display: flex;
  align-items: center;
  padding: 0.25rem 0;
  font-size: 0.875rem;
}

.modulo-item:not(:last-child) {
  border-bottom: 1px solid #f1f3f4;
}

.limites-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 0.75rem;
}

.limite-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.25rem 0;
  font-size: 0.8rem;
}

.limite-item:not(:last-child) {
  border-bottom: 1px solid #e9ecef;
}

.limite-label {
  color: #6c757d;
  font-weight: 500;
}

.limite-valor {
  color: #2c3e50;
  font-weight: 600;
}

.plano-footer {
  background: #f8f9fa;
  margin: 0.75rem -1rem -1rem -1rem;
  padding: 0.75rem 1rem;
}

.empty-state {
  padding: 3rem 1rem;
}

.status-badge .badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

/* Responsividade */
@media (max-width: 1200px) {
  .planos-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .planos-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .gerenciador-planos {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .gerenciador-planos {
    padding: 0.75rem;
  }
}

/* Animações */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.plano-card {
  animation: fadeIn 0.5s ease-out;
}

/* Dropdown customizado */
.dropdown-menu {
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

/* Dropdown customizado */
.dropdown-custom {
  position: relative;
}

.dropdown-menu-custom {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 9999;
  min-width: 160px;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.375rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
  list-style: none;
}

.dropdown-item-custom {
  display: block;
  width: 100%;
  padding: 0.375rem 1rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  transition: all 0.15s ease-in-out;
}

.dropdown-item-custom:hover,
.dropdown-item-custom:focus {
  color: #1e2125;
  background-color: #e9ecef;
}

.dropdown-divider-custom {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid rgba(0, 0, 0, 0.15);
}

/* Cards compactos */
.plano-body-compact {
  padding: 1rem;
}

.valor-compact {
  display: flex;
  align-items: baseline;
  gap: 0.25rem;
}

.limites-compact {
  font-size: 0.875rem;
}

.limites-compact .row {
  margin: 0;
}

.limites-compact .col-6 {
  padding: 0.25rem 0.5rem;
}

.plano-footer-compact {
  font-size: 0.75rem;
}
</style>
