import axios from '@/services/axios'
import router from "@/router/index";
import { jwtDecode } from 'jwt-decode';
import store from '@/store';

function isAuthenticated() {
    return localStorage.getItem('isAuthenticated', 'false') === 'true';
}

function isSystemAdmin() {
    const decoded = decodedToken();
    if (!decoded) return false;

    return typeof decoded.system_admin === 'boolean' ? decoded.system_admin : decoded.system_admin === 1;
}

function getClinica() {
    const decoded = decodedToken();
    if (!decoded) return null;
    return decoded.clinica;
}

function getAuthToken() {
    return localStorage.getItem('token');
}

function decodedToken() {
    const token = getAuthToken();
    if (!token) return null;

    try {
        const decoded = jwtDecode(token);
        return decoded;
    } catch (error) {
        console.error('Erro ao decodificar token:', error);
        return null;
    }
}

async function login(credentials) {
    try {
        const { username, password } = credentials

        const response = await axios.post('/auth/login', {
            username, password
        });

        if (!response || !response.data || !response.data.access_token)
            return false

        const data = response.data

        axios.refreshToken(data.access_token)
        localStorage.setItem('isAuthenticated', 'true');

        // Decodificar token e atualizar Vuex store
        const decodedToken = jwtDecode(data.access_token);
        store.commit('setToken', decodedToken);

        // Carregar configurações da agenda após login bem-sucedido
        await loadAgendaConfigAfterLogin();

        return true

    } catch (error) {
        console.error('Erro ao realizar login:', error);
    }

    return false
}

async function logout(callback = null) {

    try {
        await axios.post('/auth/logout')
        if (callback) callback()

        localStorage.clear()
        router.push('/entrar')

        return true

    } catch (error) {
        console.error('Erro ao realizar login:', error);
        return false
    }

}

async function refreshAuth(options = {}) {
    // Se não está autenticado, retorna null imediatamente
    if (!isAuthenticated()) {
        return null;
    }

    // Verificar se o token existe e é válido
    const token = decodedToken();
    if (!token) {
        // Token inválido, limpar autenticação
        localStorage.removeItem('isAuthenticated');
        localStorage.removeItem('token');
        return false;
    }

    // Se não é forçado e o token ainda é válido, retorna true
    if (!options?.force && token.exp > Math.floor(Date.now() / 1000)) {
        return true;
    }

    try {
        const response = await axios.post('/auth/refresh');

        if (!response || !response.data || !response.data.access_token) {
            // Falha no refresh, limpar autenticação
            localStorage.removeItem('isAuthenticated');
            localStorage.removeItem('token');
            return false;
        }

        const data = response.data;
        axios.refreshToken(data.access_token);

        // Carregar configurações da agenda após refresh do token
        await loadAgendaConfigAfterLogin();

        return true;

    } catch (error) {
        console.error('Erro ao realizar refresh do token:', error);
        // Em caso de erro, limpar autenticação
        localStorage.removeItem('isAuthenticated');
        localStorage.removeItem('token');
        return false;
    }
}

async function updateProfile(userData) {
    try {
        const response = await axios.patch('/profile', userData);

        if (!response || !response.data || response.data.status === 'error')
            return false;

        return response.data;

    } catch (error) {
        console.error('Erro ao atualizar perfil:', error);
        return false;
    }
}

async function loadAgendaConfigAfterLogin() {
    try {
        // Decodificar o token para obter as configurações da agenda
        const token = decodedToken();
        if (token && token.agenda_config) {
            // Importar o store dinamicamente para evitar dependência circular
            const { default: store } = await import('@/store');

            // Carregar as configurações no store
            await store.dispatch('agendaConfig/loadAgendaConfig', token.agenda_config);

            // Gerar os time slots
            store.dispatch('agendaConfig/generateTimeSlots');

            console.log('Configurações da agenda carregadas com sucesso');
        }
    } catch (error) {
        console.error('Erro ao carregar configurações da agenda:', error);
    }
}

export default {
    login,
    logout,
    decodedToken,
    getAuthToken,
    isSystemAdmin,
    getClinica,
    isAuthenticated,
    refreshAuth,
    updateProfile,
    loadAgendaConfigAfterLogin,
}
