<template>
    <lumi-sidenav
        icon="mdi-cog"
        class="fixed-end lumi-sidenav"
        v-if="showSidenav"
        :config="sidenavConfig"
        @action="handleSidenavAction"
    ></lumi-sidenav>

    <div class="main-page-content">
        <div v-if="selectedTab == 'perfil'" class="px-4 pt-0 pb-5 container-fluid">
            <div class="row justify-content-center">
                <div class="col-lg-12 position-relative z-index-2 py-3" style="max-width: 600px;">
                    <div class="card shadow-lg">
                        <div class="card-header p-4 bg-gradient-light text-center">
                            <h3 class="text-dark mb-0">{{ $t('profile.title') }}</h3>
                        </div>
                        <div class="card-body p-4">
                            <div class="row justify-content-center">
                                <div class="col-12 form-group justify-content-center mb-4">
                                    <div class="w-100 text-center mb-2">
                                        <label class="form-label fw-bold">{{ $t('profile.language') }}:</label>
                                    </div>
                                    <div class="d-flex flex-row justify-content-center">
                                        <div class="me-3 flag-container"
                                            :class="{
                                                selected: selectedLanguage === 'pt',
                                                'flag-loading': isChangingLanguage && selectedLanguage === 'pt'
                                            }"
                                            @click="changeLanguage('pt')"
                                        >
                                            <div v-if="isChangingLanguage && selectedLanguage === 'pt'" class="flag-loading-overlay">
                                                <div class="spinner-border spinner-border-sm text-light" role="status">
                                                    <span class="visually-hidden">{{ $t('profile.loadingText') }}</span>
                                                </div>
                                            </div>
                                            <img src="@/assets/img/flags/pt.png" alt="Português" width="90" height="60" />
                                            <span>Português</span>
                                        </div>

                                        <div class="mx-3 flag-container"
                                            :class="{
                                                selected: selectedLanguage === 'en',
                                                'flag-loading': isChangingLanguage && selectedLanguage === 'en'
                                            }"
                                            @click="changeLanguage('en')"
                                        >
                                            <div v-if="isChangingLanguage && selectedLanguage === 'en'" class="flag-loading-overlay">
                                                <div class="spinner-border spinner-border-sm text-light" role="status">
                                                    <span class="visually-hidden">{{ $t('profile.loadingText') }}</span>
                                                </div>
                                            </div>
                                            <img src="@/assets/img/flags/en.png" alt="English" width="90" height="60" />
                                            <span>English</span>
                                        </div>

                                        <div class="ms-3 flag-container"
                                            :class="{
                                                selected: selectedLanguage === 'es',
                                                'flag-loading': isChangingLanguage && selectedLanguage === 'es'
                                            }"
                                            @click="changeLanguage('es')"
                                        >
                                            <div v-if="isChangingLanguage && selectedLanguage === 'es'" class="flag-loading-overlay">
                                                <div class="spinner-border spinner-border-sm text-light" role="status">
                                                    <span class="visually-hidden">{{ $t('profile.loadingText') }}</span>
                                                </div>
                                            </div>
                                            <img src="@/assets/img/flags/es.png" alt="Español" width="90" height="60" />
                                            <span>Español</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="p-horizontal-divider mb-4"></div>

                                <form @submit.prevent="saveProfile" class="w-100">
                                    <div class="row">
                                        <div class="col-sm-7 form-group mb-3">
                                            <label for="nome" class="form-label">{{ $t('profile.fullName') }}:</label>
                                            <input
                                                type="text"
                                                id="nome"
                                                v-model="formData.nome"
                                                class="form-control"
                                                :class="{ 'is-invalid': errors.nome }"
                                            >
                                            <div v-if="errors.nome" class="invalid-feedback">{{ errors.nome }}</div>
                                        </div>
                                        <div class="col-sm-5 form-group mb-3">
                                            <label for="clinica" class="form-label">{{ $t('profile.clinic') }}:</label>
                                            <input
                                                type="text"
                                                id="clinica"
                                                v-model="formData.clinica"
                                                class="form-control"
                                                :class="{ 'is-invalid': errors.clinica }"
                                            >
                                            <div v-if="errors.clinica" class="invalid-feedback">{{ errors.clinica }}</div>
                                        </div>
                                        <div class="col-sm-7 form-group mb-3">
                                            <label for="email" class="form-label">{{ $t('profile.email') }}:</label>
                                            <input
                                                type="email"
                                                id="email"
                                                v-model="formData.email"
                                                class="form-control"
                                                :class="{ 'is-invalid': errors.email }"
                                            >
                                            <div v-if="errors.email" class="invalid-feedback">{{ errors.email }}</div>
                                        </div>

                                        <div class="col-sm-5 form-group mb-3">
                                            <label for="username" class="form-label">{{ $t('profile.username') }}:</label>
                                            <input
                                                type="text"
                                                id="username"
                                                v-model="formData.username"
                                                class="form-control"
                                                :class="{ 'is-invalid': errors.username }"
                                            >
                                            <div v-if="errors.username" class="invalid-feedback">{{ errors.username }}</div>
                                        </div>

                                        <div class="p-horizontal-divider my-3"></div>

                                        <div class="col-sm-6 form-group mb-3">
                                            <label for="senha" class="form-label">{{ $t('profile.newPassword') }}:</label>
                                            <input
                                                type="password"
                                                id="senha"
                                                v-model="formData.password"
                                                class="form-control"
                                                placeholder="••••••••"
                                                :class="{ 'is-invalid': errors.password }"
                                            >
                                            <small class="form-text text-muted">{{ $t('profile.passwordHint') }}</small>
                                            <div v-if="errors.password" class="invalid-feedback">{{ errors.password }}</div>
                                        </div>
                                        <div class="col-sm-6 form-group mb-3">
                                            <label for="senha_confirmacao" class="form-label">{{ $t('profile.confirmPassword') }}:</label>
                                            <input
                                                type="password"
                                                id="senha_confirmacao"
                                                v-model="formData.password_confirmation"
                                                class="form-control"
                                                placeholder="••••••••"
                                                :class="{ 'is-invalid': errors.password_confirmation }"
                                            >
                                            <small class="form-text text-muted">{{ $t('profile.confirmPasswordHint') }}</small>
                                            <div v-if="errors.password_confirmation" class="invalid-feedback">{{ errors.password_confirmation }}</div>
                                        </div>
                                    </div>

                                    <div class="w-100 text-center mt-4">
                                        <button
                                            type="submit"
                                            class="btn btn-primary px-5"
                                            :disabled="isSubmitting"
                                        >
                                            <span v-if="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                                            <i v-else class="fas fa-check me-2"></i>
                                            {{ isSubmitting ? $t('profile.savingButton') : $t('profile.saveButton') }}
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <div v-if="selectedTab == 'registros'" class="p-0 container-fluid">
            <div class="row mb-4">
                <div class="col-lg-12 position-relative z-index-2">
                    <ActionHistoryTable />
                </div>
            </div>
        </div>

        <!-- Tabela de Preços -->
        <div v-if="selectedTab == 'precos'" class="p-0 container-fluid">
            <div class="row mb-4">
                <div class="col-lg-12 position-relative z-index-2">
                    <TabelaPrecos />
                </div>
            </div>
        </div>

        <!-- Configurações da Agenda -->
        <div v-if="selectedTab == 'agenda'" class="p-0 container-fluid">
            <div class="row mb-4">
                <div class="col-lg-12 position-relative z-index-2">
                    <ConfiguracoesAgenda />
                </div>
            </div>
        </div>
    </div>
</template>
<style scoped>
.flag-container {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    /* Altura mínima para evitar layout shift */
    min-height: 95px;
    display: flex;
    flex-direction: column;
}

.flag-container span {
    text-transform: uppercase;
    font-weight: 500;
    font-size: 9pt;
    padding: 5px 0;
    display: block;
}

.flag-container img {
    border-radius: 5px 5px 0px 0px;
    transition: transform 0.3s ease;
    width: 90px;
    height: 60px;
    object-fit: cover;
    display: block;
    /* Reserva o espaço da imagem para evitar layout shift */
    min-height: 60px;
    background-color: #f8f9fa;
}

.flag-container:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.flag-container:hover img {
    transform: scale(1.05);
}

.flag-container.selected {
    border-color: #007bff;
    background: #007bff;
    color: #FFF;
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.2);
}

.flag-container.selected:hover {
    box-shadow: 0 8px 20px rgba(0, 123, 255, 0.3);
}

.flag-container.flag-loading {
    position: relative;
    cursor: not-allowed;
}

.flag-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 123, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    border-radius: 8px;
}

.card {
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.card-header h3 {
    font-weight: 600;
    color: #495057;
    font-size: 1.25rem;
    letter-spacing: 0.01em;
}

.form-control {
    border-radius: 6px;
    padding: 10px 15px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.15);
}

.btn-primary {
    border-radius: 6px;
    padding: 10px 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 123, 255, 0.2);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 123, 255, 0.3);
}

.btn-primary:active {
    transform: translateY(0);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #495057;
}

.p-horizontal-divider {
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.1), transparent);
    margin: 1rem 0;
}

.form-text {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    color: #6c757d;
}

small.form-text {
    display: block;
    margin-top: 0.25rem;
}
</style>
<script>

import { mapMutations, mapState } from "vuex";

import LumiSidenav from "@/views/components/LumiSidenav/index.vue";
import ActionHistoryTable from "@/components/ActionHistoryTable.vue";
import TabelaPrecos from "@/components/TabelaPrecos.vue";
import ConfiguracoesAgenda from "@/views/ConfiguracoesAgenda.vue";

import usuariosService from "@/services/usuariosService";
import cSwal from "@/utils/cSwal.js";
import { setI18nLanguage } from "@/i18n";

export default {
    name: "configuracoes",
    data() {

        return {
            selectedTab: 'perfil',
            sidenavConfig: null,
            selectedLanguage: this.$user?.language === 'br' ? 'pt' : (this.$user?.language || 'pt'),
            formData: {
                nome: '',
                clinica: '',
                email: '',
                username: '',
                password: '',
                password_confirmation: ''
            },
            errors: {},
            isSubmitting: false,
            isChangingLanguage: false
        };
    },
    watch: {
        selectedTab: {
            immediate: true,
            handler(newVal) {
                this.updateSidenavConfig();
            }
        }
    },
    components: {
        LumiSidenav,
        ActionHistoryTable,
        TabelaPrecos,
        ConfiguracoesAgenda,
    },
    methods: {
        ...mapMutations(['setToken']),
        updateSidenavConfig() {
            this.sidenavConfig = {
                groups: [
                    {
                        title: "CONFIGURAÇÕES",
                        buttons: [
                            {
                                text: "Meu perfil",
                                icon: ["fas", "user"],
                                iconType: "font-awesome",
                                action: "changeTab",
                                actionData: "perfil",
                                active: this.selectedTab === 'perfil'
                            }
                        ]
                    },
                    {
                        title: "CLÍNICA",
                        buttons: [
                            {
                                text: "Tabela de Preços",
                                icon: ["fas", "list-alt"],
                                iconType: "font-awesome",
                                action: "changeTab",
                                actionData: "precos",
                                active: this.selectedTab === 'precos',
                                class: "text-success",
                                textClass: "text-success"
                            },
                            {
                                text: "Configurações da Agenda",
                                icon: ["fas", "calendar-alt"],
                                iconType: "font-awesome",
                                action: "changeTab",
                                actionData: "agenda",
                                active: this.selectedTab === 'agenda',
                                class: "text-primary",
                                textClass: "text-primary"
                            }
                        ]
                    },
                    {
                        title: "SISTEMA",
                        buttons: [
                            {
                                text: "Histórico de Ações",
                                icon: ["fas", "history"],
                                iconType: "font-awesome",
                                action: "changeTab",
                                actionData: "registros",
                                active: this.selectedTab === 'registros',
                                class: "text-info",
                                textClass: "text-info"
                            }
                        ]
                    }
                ]
            };
        },
        handleSidenavAction(action, button) {
            console.log(`Action: ${action}`, button);

            // Implementar as ações da sidenav
            if (action === 'changeTab') {
                this.changeTab(button.actionData);
            }
        },

        changeTab(tab) {
            this.selectedTab = tab;
            this.$emit('changeTab', tab);
        },
        async changeLanguage(lang) {
            if (this.selectedLanguage === lang || this.isChangingLanguage) {
                return; // Não fazer nada se o idioma já estiver selecionado ou se já estiver em processo de mudança
            }

            const previousLanguage = this.selectedLanguage;
            this.selectedLanguage = lang;
            this.isChangingLanguage = true;

            // Mapear 'pt' para 'br' para compatibilidade com o backend
            const backendLang = lang === 'pt' ? 'br' : lang;
            const i18nLang = lang;

            // Salvar a preferência de idioma imediatamente
            try {
                const userData = {
                    language: backendLang
                };

                const result = await usuariosService.updateProfile(userData);

                if (result) {
                    // Atualizar o idioma da aplicação
                    setI18nLanguage(i18nLang);

                    // Atualizar o token para refletir as alterações
                    try {
                        const refreshResult = await usuariosService.refreshAuth({ force: true });

                        if (refreshResult) {
                            // Atualizar o token no Vuex store
                            const decodedToken = usuariosService.decodedToken();
                            if (decodedToken) {
                                this.setToken(decodedToken);
                            }
                        }
                    } catch (refreshError) {
                        console.error('Erro ao atualizar token após mudança de idioma:', refreshError);
                    }
                } else {
                    // Reverter para o idioma anterior em caso de erro
                    this.selectedLanguage = previousLanguage;
                    cSwal.cError('Erro ao atualizar o idioma. Por favor, tente novamente.');
                }
            } catch (error) {
                console.error('Erro ao salvar preferência de idioma:', error);
                this.selectedLanguage = previousLanguage;
                cSwal.cError('Erro ao atualizar o idioma: ' + (error.message || 'Erro desconhecido'));
            } finally {
                this.isChangingLanguage = false;
            }
        },
        loadUserData() {
            // Preencher os campos com os dados do usuário logado
            if (this.$dentista) {
                this.formData.nome = this.$dentista.nome || '';
            }

            if (this.$clinica) {
                this.formData.clinica = this.$clinica.nome || '';
            }

            if (this.$user) {
                this.formData.email = this.$user.email || '';
                this.formData.username = this.$user.username || '';

                // Carregar o idioma do usuário
                if (this.$user.language) {
                    // Mapear 'br' para 'pt' para compatibilidade com o frontend
                    this.selectedLanguage = this.$user.language === 'br' ? 'pt' : this.$user.language;
                    // console.log('Idioma carregado do usuário:', this.$user.language, '→', this.selectedLanguage);

                    // Atualizar o idioma da aplicação
                    setI18nLanguage(this.selectedLanguage);
                } else {
                    // console.log('Usuário não tem idioma definido, usando padrão:', this.selectedLanguage);
                }
            }
        },
        validateForm() {
            this.errors = {};
            let isValid = true;

            if (!this.formData.nome || this.formData.nome.trim() === '') {
                this.errors.nome = 'O nome é obrigatório';
                isValid = false;
            }

            if (!this.formData.clinica || this.formData.clinica.trim() === '') {
                this.errors.clinica = 'O nome da clínica é obrigatório';
                isValid = false;
            }

            // if (!this.formData.email || this.formData.email.trim() === '') {
            //     this.errors.email = 'O e-mail é obrigatório';
            //     isValid = false;
            // } else

            if (this.formData.email.trim() !== '' && !this.validateEmail(this.formData.email)) {
                this.errors.email = 'E-mail inválido';
                isValid = false;
            }

            if (!this.formData.username || this.formData.username.trim() === '') {
                this.errors.username = 'O nome de usuário é obrigatório';
                isValid = false;
            }

            // Validar senha apenas se foi preenchida
            if (this.formData.password) {
                if (this.formData.password.length < 8) {
                    this.errors.password = 'A senha deve ter pelo menos 8 caracteres';
                    isValid = false;
                }

                if (this.formData.password !== this.formData.password_confirmation) {
                    this.errors.password_confirmation = 'As senhas não coincidem';
                    isValid = false;
                }
            }

            return isValid;
        },
        validateEmail(email) {
            const re = /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            return re.test(String(email).toLowerCase());
        },
        async saveProfile() {
            if (!this.validateForm()) {
                return;
            }

            this.isSubmitting = true;

            try {
                // Preparar dados para envio
                const userData = {
                    nome: this.formData.nome,
                    clinica: this.formData.clinica,
                    email: this.formData.email,
                    username: this.formData.username,
                    language: this.selectedLanguage
                };

                // Adicionar senha apenas se foi preenchida
                if (this.formData.password) {
                    userData.password = this.formData.password;
                }

                const result = await usuariosService.updateProfile(userData);

                if (result) {
                    cSwal.cSuccess('Perfil atualizado com sucesso!');

                    // Limpar campos de senha após salvar
                    this.formData.password = '';
                    this.formData.password_confirmation = '';

                    // Mapear 'pt' para 'br' para compatibilidade com o backend
                    const i18nLang = this.selectedLanguage;

                    // Atualizar o idioma da aplicação
                    setI18nLanguage(i18nLang);

                    // Atualizar o token para refletir as alterações
                    try {
                        const refreshResult = await usuariosService.refreshAuth({ force: true });

                        if (refreshResult) {
                            // Atualizar o token no Vuex store
                            const decodedToken = usuariosService.decodedToken();
                            if (decodedToken) {
                                this.setToken(decodedToken);
                            }

                            // Atualizar os dados do formulário com os novos valores
                            this.loadUserData();
                        }
                    } catch (refreshError) {
                        console.error('Erro ao atualizar token:', refreshError);
                    }
                } else {
                    cSwal.cError('Erro ao atualizar o perfil. Por favor, tente novamente.');
                }
            } catch (error) {
                console.error('Erro ao salvar perfil:', error);
                cSwal.cError('Erro ao atualizar o perfil: ' + (error.message || 'Erro desconhecido'));
            } finally {
                this.isSubmitting = false;
            }
        }
    },
    computed: {
        ...mapState([
            "isRTL",
            "color",
            "isAbsolute",
            "isNavFixed",
            "navbarFixed",
            "absolute",
            "showSidenav",
            "showNavbar",
            "showFooter",
            "showConfig",
            "hideConfigButton",
        ]),
    },

    async created() {
        // Primeiro carregamos os dados do usuário para garantir que o idioma seja configurado corretamente
        this.loadUserData();

        // Verificação adicional para garantir que o idioma está correto
        if (this.$user && this.$user.language) {
            const correctLanguage = this.$user.language === 'br' ? 'pt' : this.$user.language;
            if (this.selectedLanguage !== correctLanguage) {
                console.log('Corrigindo idioma:', this.selectedLanguage, '→', correctLanguage);
                this.selectedLanguage = correctLanguage;
            }
        }
    },
};
</script>