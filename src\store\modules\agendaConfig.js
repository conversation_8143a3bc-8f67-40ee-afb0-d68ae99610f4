// Store module para gerenciar configurações da agenda por consultório
const state = {
  // Configurações por consultório (chave: consultorioId, valor: config)
  configsByConsultorio: {},

  // Consultório atualmente selecionado
  selectedConsultorioId: null,

  // Lista de consultórios disponíveis
  consultorios: [],

  // Estados de carregamento
  isLoaded: false,
  isLoadingConsultorios: false
};

const mutations = {
  SET_CONSULTORIOS(state, consultorios) {
    state.consultorios = consultorios;
    state.isLoadingConsultorios = false;
  },

  SET_LOADING_CONSULTORIOS(state, loading) {
    state.isLoadingConsultorios = loading;
  },

  SET_SELECTED_CONSULTORIO(state, consultorioId) {
    state.selectedConsultorioId = consultorioId;
  },

  SET_AGENDA_CONFIG_FOR_CONSULTORIO(state, { consultorioId, config }) {
    if (!state.configsByConsultorio[consultorioId]) {
      state.configsByConsultorio[consultorioId] = {};
    }
    state.configsByConsultorio[consultorioId] = { ...state.configsByConsultorio[consultorioId], ...config };
    state.isLoaded = true;
  },

  UPDATE_AGENDA_CONFIG(state, updates) {
    state.config = { ...state.config, ...updates };
  },

  SET_TIME_SLOTS_FOR_CONSULTORIO(state, { consultorioId, timeSlots }) {
    if (!state.configsByConsultorio[consultorioId]) {
      state.configsByConsultorio[consultorioId] = {};
    }
    state.configsByConsultorio[consultorioId].time_slots = timeSlots;
  },

  RESET_AGENDA_CONFIG_FOR_CONSULTORIO(state, consultorioId) {
    const defaultConfig = {
      horario_inicio: '08:00',
      horario_fim: '18:00',
      dias_semana: ['segunda', 'terca', 'quarta', 'quinta', 'sexta'],
      duracao_padrao_consulta: 30,
      permitir_duracao_personalizada: true,
      intervalo_entre_consultas: 0,
      tem_horario_almoco: false,
      horario_almoco_inicio: null,
      horario_almoco_fim: null,
      permitir_agendamento_passado: false,
      permitir_agendamento_feriados: false,
      antecedencia_minima_agendamento: 0,
      antecedencia_maxima_agendamento: 720,
      time_slots: []
    };
    state.configsByConsultorio[consultorioId] = defaultConfig;
  },

  CLEAR_ALL_CONFIGS(state) {
    state.configsByConsultorio = {};
    state.selectedConsultorioId = null;
    state.isLoaded = false;
  }
};

const actions = {
  async loadConsultorios({ commit }) {
    try {
      commit('SET_LOADING_CONSULTORIOS', true);

      const consultorioService = (await import('@/services/consultorioService.js')).default;
      const consultorios = await consultorioService.getConsultorios();

      commit('SET_CONSULTORIOS', consultorios || []);

      // Se há consultórios e nenhum está selecionado, selecionar o primeiro
      if (consultorios && consultorios.length > 0) {
        commit('SET_SELECTED_CONSULTORIO', consultorios[0].id);
      }

      return consultorios;
    } catch (error) {
      console.error('Erro ao carregar consultórios:', error);
      commit('SET_LOADING_CONSULTORIOS', false);
      return [];
    }
  },

  async loadAgendaConfigForConsultorio({ commit }, { consultorioId, config }) {
    try {
      if (config) {
        commit('SET_AGENDA_CONFIG_FOR_CONSULTORIO', { consultorioId, config });
        return true;
      }

      // Se não foi passada configuração, tentar carregar da API
      const agendaConfigService = (await import('@/services/agendaConfigService.js')).default;
      const loadedConfig = await agendaConfigService.getAgendaConfig(consultorioId);

      if (loadedConfig) {
        commit('SET_AGENDA_CONFIG_FOR_CONSULTORIO', { consultorioId, config: loadedConfig });
        return true;
      }

      return false;
    } catch (error) {
      console.error('Erro ao carregar configurações da agenda:', error);
      return false;
    }
  },

  // Manter compatibilidade com código existente
  async loadAgendaConfig({ dispatch, state }, config) {
    if (state.selectedConsultorioId) {
      return await dispatch('loadAgendaConfigForConsultorio', {
        consultorioId: state.selectedConsultorioId,
        config
      });
    }
    return false;
  },
  
  async updateAgendaConfig({ commit }, updates) {
    try {
      const axios = (await import('@/services/axios.js')).default;
      const response = await axios.patch('/agenda-config', updates);
      
      if (response.data && response.data.status === 'success') {
        commit('UPDATE_AGENDA_CONFIG', response.data.data);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Erro ao atualizar configurações da agenda:', error);
      throw error;
    }
  },
  
  async resetAgendaConfig({ commit }) {
    try {
      const axios = (await import('@/services/axios.js')).default;
      const response = await axios.post('/agenda-config/reset');
      
      if (response.data && response.data.status === 'success') {
        commit('SET_AGENDA_CONFIG', response.data.data);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Erro ao resetar configurações da agenda:', error);
      throw error;
    }
  },
  
  generateTimeSlots({ state, commit }) {
    try {
      const config = state.config;
      const slots = [];
      
      // Verificar se há configurações por dia ou usar formato antigo
      let horarioConfig = null;

      if (config.configuracoes_por_dia && typeof config.configuracoes_por_dia === 'object') {
        const dias = Object.keys(config.configuracoes_por_dia);
        if (dias.length > 0) {
          horarioConfig = config.configuracoes_por_dia[dias[0]];
        }
      } else if (config.horario_inicio && config.horario_fim) {
        // Fallback para formato antigo
        horarioConfig = {
          horario_inicio: config.horario_inicio,
          horario_fim: config.horario_fim,
          tem_horario_almoco: config.tem_horario_almoco,
          horario_almoco_inicio: config.horario_almoco_inicio,
          horario_almoco_fim: config.horario_almoco_fim
        };
      }

      if (!horarioConfig || !horarioConfig.horario_inicio || !horarioConfig.horario_fim) {
        commit('SET_TIME_SLOTS', []);
        return [];
      }

      // Converter horários para objetos Date
      const [horaInicio, minutoInicio] = horarioConfig.horario_inicio.split(':').map(Number);
      const [horaFim, minutoFim] = horarioConfig.horario_fim.split(':').map(Number);
      
      const inicio = new Date();
      inicio.setHours(horaInicio, minutoInicio, 0, 0);
      
      const fim = new Date();
      fim.setHours(horaFim, minutoFim, 0, 0);
      
      const current = new Date(inicio);
      
      while (current < fim) {
        // Verificar se não está no horário de almoço
        if (horarioConfig.tem_horario_almoco && horarioConfig.horario_almoco_inicio && horarioConfig.horario_almoco_fim) {
          const [horaAlmocoInicio, minutoAlmocoInicio] = horarioConfig.horario_almoco_inicio.split(':').map(Number);
          const [horaAlmocoFim, minutoAlmocoFim] = horarioConfig.horario_almoco_fim.split(':').map(Number);
          
          const almocoInicio = new Date();
          almocoInicio.setHours(horaAlmocoInicio, minutoAlmocoInicio, 0, 0);
          
          const almocoFim = new Date();
          almocoFim.setHours(horaAlmocoFim, minutoAlmocoFim, 0, 0);
          
          if (current >= almocoInicio && current < almocoFim) {
            current.setTime(current.getTime() + (config.duracao_padrao_consulta + config.intervalo_entre_consultas) * 60000);
            continue;
          }
        }
        
        const timeString = current.toTimeString().substring(0, 5);
        slots.push(timeString);
        
        // Adicionar duração da consulta + intervalo
        current.setTime(current.getTime() + (config.duracao_padrao_consulta + config.intervalo_entre_consultas) * 60000);
      }
      
      commit('SET_TIME_SLOTS', slots);
      return slots;
    } catch (error) {
      console.error('Erro ao gerar horários:', error);
      return [];
    }
  },

  // Novos métodos para consultórios
  generateTimeSlotsForConsultorio({ commit, state }, consultorioId) {
    try {
      const config = state.configsByConsultorio[consultorioId];

      if (!config || !config.horario_inicio || !config.horario_fim) {
        commit('SET_TIME_SLOTS_FOR_CONSULTORIO', { consultorioId, timeSlots: [] });
        return;
      }

      const slots = [];
      const [startHour, startMinute] = config.horario_inicio.split(':').map(Number);
      const [endHour, endMinute] = config.horario_fim.split(':').map(Number);

      const start = new Date();
      start.setHours(startHour, startMinute, 0, 0);

      const end = new Date();
      end.setHours(endHour, endMinute, 0, 0);

      const current = new Date(start);

      while (current < end) {
        const timeString = current.toTimeString().substring(0, 5);

        // Verificar se não está no horário de almoço
        let isLunchTime = false;
        if (config.tem_horario_almoco && config.horario_almoco_inicio && config.horario_almoco_fim) {
          const [lunchStartHour, lunchStartMinute] = config.horario_almoco_inicio.split(':').map(Number);
          const [lunchEndHour, lunchEndMinute] = config.horario_almoco_fim.split(':').map(Number);

          const currentMinutes = current.getHours() * 60 + current.getMinutes();
          const lunchStartMinutes = lunchStartHour * 60 + lunchStartMinute;
          const lunchEndMinutes = lunchEndHour * 60 + lunchEndMinute;

          isLunchTime = currentMinutes >= lunchStartMinutes && currentMinutes < lunchEndMinutes;
        }

        if (!isLunchTime) {
          slots.push(timeString);
        }

        // Adicionar duração da consulta + intervalo
        const intervalMinutes = (config.duracao_padrao_consulta || 30) + (config.intervalo_entre_consultas || 0);
        current.setTime(current.getTime() + intervalMinutes * 60000);
      }

      commit('SET_TIME_SLOTS_FOR_CONSULTORIO', { consultorioId, timeSlots: slots });
    } catch (error) {
      console.error('Erro ao gerar horários:', error);
      commit('SET_TIME_SLOTS_FOR_CONSULTORIO', { consultorioId, timeSlots: [] });
    }
  },

  selectConsultorio({ commit, dispatch }, consultorioId) {
    commit('SET_SELECTED_CONSULTORIO', consultorioId);
    dispatch('generateTimeSlotsForConsultorio', consultorioId);
  }
};

const getters = {
  // Getters para consultórios
  consultorios: state => state.consultorios,
  selectedConsultorioId: state => state.selectedConsultorioId,
  selectedConsultorio: state => {
    return state.consultorios.find(c => c.id === state.selectedConsultorioId) || null;
  },
  isLoadingConsultorios: state => state.isLoadingConsultorios,

  // Getters para configurações por consultório
  agendaConfigForConsultorio: state => consultorioId => {
    return state.configsByConsultorio[consultorioId] || null;
  },

  currentAgendaConfig: state => {
    if (!state.selectedConsultorioId) return null;
    return state.configsByConsultorio[state.selectedConsultorioId] || null;
  },

  timeSlotsForConsultorio: state => consultorioId => {
    const config = state.configsByConsultorio[consultorioId];
    return config ? config.time_slots || [] : [];
  },

  currentTimeSlots: state => {
    if (!state.selectedConsultorioId) return [];
    const config = state.configsByConsultorio[state.selectedConsultorioId];
    return config ? config.time_slots || [] : [];
  },

  // Getters de compatibilidade (mantém funcionamento do código existente)
  agendaConfig: state => {
    if (!state.selectedConsultorioId) return null;
    return state.configsByConsultorio[state.selectedConsultorioId] || null;
  },

  isAgendaConfigLoaded: state => state.isLoaded,

  timeSlots: state => {
    if (!state.selectedConsultorioId) return [];
    const config = state.configsByConsultorio[state.selectedConsultorioId];
    return config ? config.time_slots || [] : [];
  },

  // Getter para verificar se um dia da semana está ativo
  isDayActive: state => dayOfWeek => {
    if (!state.selectedConsultorioId) return false;
    const config = state.configsByConsultorio[state.selectedConsultorioId];
    return config ? config.dias_semana.includes(dayOfWeek) : false;
  },

  // Getter para obter configurações formatadas para o LumiCalendar
  calendarConfig: state => {
    if (!state.selectedConsultorioId) return null;
    const config = state.configsByConsultorio[state.selectedConsultorioId];
    if (!config) return null;

    return {
      horario_inicio: config.horario_inicio,
      horario_fim: config.horario_fim,
      dias_semana: config.dias_semana,
      duracao_padrao_consulta: config.duracao_padrao_consulta,
      time_slots: config.time_slots || []
    };
  },
  
  // Getter para verificar se está no horário de funcionamento
  isWorkingTime: state => (dayOfWeek, time) => {
    // Verificar se o dia está ativo
    if (!state.config.dias_semana.includes(dayOfWeek)) {
      return false;
    }
    
    // Verificar se está dentro do horário de funcionamento
    const [hora, minuto] = time.split(':').map(Number);
    const [horaInicio, minutoInicio] = state.config.horario_inicio.split(':').map(Number);
    const [horaFim, minutoFim] = state.config.horario_fim.split(':').map(Number);
    
    const timeMinutes = hora * 60 + minuto;
    const inicioMinutes = horaInicio * 60 + minutoInicio;
    const fimMinutes = horaFim * 60 + minutoFim;
    
    if (timeMinutes < inicioMinutes || timeMinutes >= fimMinutes) {
      return false;
    }
    
    // Verificar se não está no horário de almoço
    if (state.config.tem_horario_almoco && state.config.horario_almoco_inicio && state.config.horario_almoco_fim) {
      const [horaAlmocoInicio, minutoAlmocoInicio] = state.config.horario_almoco_inicio.split(':').map(Number);
      const [horaAlmocoFim, minutoAlmocoFim] = state.config.horario_almoco_fim.split(':').map(Number);
      
      const almocoInicioMinutes = horaAlmocoInicio * 60 + minutoAlmocoInicio;
      const almocoFimMinutes = horaAlmocoFim * 60 + minutoAlmocoFim;
      
      if (timeMinutes >= almocoInicioMinutes && timeMinutes < almocoFimMinutes) {
        return false;
      }
    }
    
    return true;
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
