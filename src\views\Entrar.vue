<template>
  <div class="page-header align-items-start min-vh-100" v-bind:style="backgroundImage" :class="{ 'breathing': showAnimations }">
    <!-- Background animado -->
    <span class="mask bg-gradient-dark opacity-6" :class="{ 'animated-mask': showAnimations }"></span>

    <!-- Login submission loading overlay -->
    <div
      v-if="isLoggingIn"
      class="login-loading-overlay"
      :class="{ 'show': isLoggingIn }"
    ></div>

    <div class="container my-auto" :class="{ 'animated-container': showAnimations }">
      <div class="row">
        <div class="col-lg-4 col-md-8 col-12 mx-auto">
          <div class="card z-index-0 login-card" :class="{ 'logging-in': isLoggingIn, 'animated-card': showAnimations }">
            <!-- Header do card com logo -->
            <div class="card-header p-0 position-relative mt-n4 mx-3 z-index-2" :class="{ 'animated-header': showAnimations }">
              <div
                class="shadow-secondary border-radius-lg py-3 bg-gradient-lumi"
                style="border: 1px solid #d2d2d2;"
              >
                <img :src="LumiBlueLogo" class="login-page-logo" :class="{ 'animated-logo': showAnimations }" />
              </div>
            </div>

            <!-- Corpo do card com formulário -->
            <div class="card-body" :class="{ 'animated-body': showAnimations }">
              <!-- Formulário de Login -->
              <form v-if="!mostrandoRecuperacao" role="form" class="text-start mt-3" @submit.prevent="submitLogin">
                <!-- Campo usuário -->
                <div class="mb-3 input-with-icon" :class="{ 'animated-field': showAnimations }" :style="showAnimations ? 'animation-delay: 0.4s' : ''">
                  <div class="input-icon-wrapper">
                    <i class="fas fa-user input-icon"></i>
                    <MaterialInput
                      id="username"
                      type="text"
                      placeholder="Usuário"
                      name="username"
                      v-model="credentials.username"
                      class="input-with-left-icon"
                    />
                  </div>
                </div>

                <!-- Campo senha -->
                <div class="mb-3 input-with-icon" :class="{ 'animated-field': showAnimations }" :style="showAnimations ? 'animation-delay: 0.5s' : ''">
                  <div class="input-icon-wrapper">
                    <i class="fas fa-lock input-icon"></i>
                    <MaterialInput
                      id="senha"
                      :type="showPassword ? 'text' : 'password'"
                      placeholder="Senha"
                      v-model="credentials.password"
                      name="senha"
                      class="input-with-left-icon input-with-right-icon"
                    />
                    <button
                      type="button"
                      class="password-toggle-btn"
                      @click="togglePasswordVisibility"
                      tabindex="-1"
                    >
                      <i :class="showPassword ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
                    </button>
                  </div>
                </div>

                <!-- Switch lembrar dispositivo -->
                <div :class="{ 'animated-field': showAnimations }" :style="showAnimations ? 'animation-delay: 0.6s' : ''">
                  <material-switch
                    id="rememberMe"
                    name="rememberMe"
                    :checked="rememberDevice"
                    @change="rememberDevice = $event"
                    >Manter este dispositivo conectado</material-switch
                  >
                </div>

                <!-- Botão de login -->
                <div class="text-center" :class="{ 'animated-field': showAnimations }" :style="showAnimations ? 'animation-delay: 0.7s' : ''">
                  <material-button
                    class="my-4 mb-2"
                    variant="gradient"
                    color="secondary"
                    fullWidth
                    :loading="isLoggingIn"
                    :loadingText="$t('login.loggingIn')"
                  >
                    {{ $t("login.submitAction") }}
                  </material-button>
                </div>

                <!-- Link Esqueceu a senha -->
                <div class="text-center mb-3" :class="{ 'animated-field': showAnimations }" :style="showAnimations ? 'animation-delay: 0.75s' : ''">
                  <a
                    href="#"
                    @click.prevent="mostrarFormularioRecuperacao"
                    class="forgot-password-link"
                  >
                    Esqueceu a senha?
                  </a>
                </div>

                <!-- Link para teste gratuito -->
                <div class="mt-4 text-sm text-center w-100" :class="{ 'animated-field': showAnimations }" :style="showAnimations ? 'animation-delay: 0.8s' : ''">
                  <a
                    href="#"
                    @click.prevent="abrirModalTesteGratuito"
                    class="text-decoration-none trial-link"
                  >
                    <div>Ainda não é cliente?</div>
                    <div><b>Teste gratuitamente!</b></div>
                  </a>
                </div>
              </form>

              <!-- Formulário de Recuperação de Senha - REIMPLEMENTADO -->
              <div v-else class="password-recovery-container">
                <!-- Botão Voltar -->
                <div class="recovery-back-button">
                  <button
                    type="button"
                    @click="voltarParaLogin"
                    class="btn-back-to-login"
                  >
                    <i class="fas fa-arrow-left"></i>
                    <span>Voltar</span>
                  </button>
                </div>

                <!-- Título -->
                <div class="recovery-title-section">
                  <h4 class="recovery-main-title">Recuperar Senha</h4>
                  <p class="recovery-description">Digite seu e-mail cadastrado para receber o link de redefinição de senha</p>
                </div>

                <!-- Formulário -->
                <form @submit.prevent="enviarLinkRecuperacao" class="recovery-form-new">
                  <!-- Campo E-mail -->
                  <div class="form-group-recovery">
                    <label for="email-recovery-input" class="recovery-label">
                      <i class="fas fa-envelope"></i>
                      E-mail
                    </label>
                    <input
                      id="email-recovery-input"
                      type="email"
                      v-model="emailRecuperacao"
                      class="recovery-input"
                      placeholder="<EMAIL>"
                      required
                      :disabled="enviandoRecuperacao"
                    />
                  </div>

                  <!-- Botão Enviar -->
                  <button
                    type="submit"
                    class="btn-send-recovery"
                    :disabled="enviandoRecuperacao"
                  >
                    <span v-if="!enviandoRecuperacao">
                      <i class="fas fa-paper-plane"></i>
                      Enviar Link de Recuperação
                    </span>
                    <span v-else>
                      <i class="fas fa-spinner fa-spin"></i>
                      Enviando...
                    </span>
                  </button>

                  <!-- Mensagem de Sucesso -->
                  <div v-if="mensagemSucesso" class="recovery-alert recovery-alert-success">
                    <i class="fas fa-check-circle"></i>
                    <div>
                      <strong>E-mail enviado!</strong>
                      <p>{{ mensagemSucesso }}</p>
                    </div>
                  </div>

                  <!-- Mensagem de Erro -->
                  <div v-if="erroEmailRecuperacao && !mensagemSucesso" class="recovery-alert recovery-alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <div>
                      <strong>Erro!</strong>
                      <p>{{ erroEmailRecuperacao }}</p>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Footer animado -->
    <footer class="footer position-absolute bottom-2 py-2 w-100" :class="{ 'animated-footer': showAnimations }">
      <div class="container">
        <div class="row align-items-center justify-content-lg-between">
          <div class="col-12 my-auto">
            <div
              class="copyright text-center text-sm text-white text-lg-start d-flex flex-column"
              style="font-weight: 400"
            >
              <span style="font-size: 11pt"
                >© {{ new Date().getFullYear() }} LUMI Vision</span
              >
            </div>
          </div>
        </div>
      </div>
    </footer>

    <!-- Modais de Trial -->
    <TrialPlanSelectorModal
      modalId="trialPlanSelectorModal"
      ref="trialPlanSelectorModal"
      @plano-selecionado="onPlanoSelecionado"
    />
    
    <TrialSignupModal
      modalId="trialSignupModal"
      ref="trialSignupModal"
      :plano-selecionado="planoSelecionadoParaTrial"
      @signup-success="onTrialSignupSuccess"
      @signup-error="onTrialSignupError"
      @voltar="onVoltarParaSelecaoPlano"
    />
  </div>
</template>
<style scoped>
/* ===== MELHORIAS SUTIS E MODERNAS ===== */

@media (min-width: 960px) {
  .card {
    min-width: 380px;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  }
}

/* Melhorar espaçamento do card body */
.card-body {
  padding: 1.75rem 2rem 2rem 2rem;
}

/* ===== FIX Z-INDEX - Garantir hierarquia correta ===== */
.mask {
  z-index: 1 !important;
}

.container {
  position: relative;
  z-index: 5 !important;
}

.card,
.login-card {
  position: relative;
  z-index: 10 !important;
}

.card-body {
  position: relative;
  z-index: 15 !important;
}

/* Melhorar espaçamento entre campos */
.mb-3 {
  margin-bottom: 1.25rem !important;
}

/* Container para inputs com ícones */
.input-with-icon {
  position: relative;
}

.input-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

/* Ícone à esquerda */
.input-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
  font-size: 16px;
  z-index: 2;
  transition: color 0.3s ease;
}

/* Input com ícone à esquerda */
:deep(.input-with-left-icon) {
  padding-left: 48px !important;
}

/* Input com ícone à direita também */
:deep(.input-with-right-icon) {
  padding-right: 48px !important;
}

/* Botão toggle da senha */
.password-toggle-btn {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  font-size: 16px;
  cursor: pointer;
  z-index: 2;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
  outline: none;
}

.password-toggle-btn:hover {
  color: #56809F;
  background-color: rgba(86, 128, 159, 0.1);
}

.password-toggle-btn:focus {
  color: #56809F;
  box-shadow: 0 0 0 2px rgba(86, 128, 159, 0.2);
}

/* Melhorar altura dos inputs */
:deep(.form-control) {
  height: 48px;
  border-radius: 8px;
  border: 1.5px solid #e9ecef;
  transition: all 0.3s ease;
  font-size: 15px;
}

:deep(.form-control:focus) {
  border-color: #56809F;
  box-shadow: 0 0 0 2px rgba(86, 128, 159, 0.1);
  transform: translateY(-1px);
}

/* Quando o input tem foco, mudar cor do ícone */
:deep(.form-control:focus) ~ .input-icon,
.input-icon-wrapper:focus-within .input-icon {
  color: #56809F;
}

/* Placeholder mais elegante */
:deep(.form-control::placeholder) {
  color: #9ca3af;
  font-weight: 400;
}

/* Melhorar botão */
:deep(.btn) {
  height: 48px;
  border-radius: 10px;
  font-weight: 500;
  letter-spacing: 0.025em;
  transition: all 0.3s ease;
}

:deep(.btn:hover:not(:disabled)) {
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(86, 128, 159, 0.25);
}

/* Melhorar switch - corrigir alinhamento */
:deep(.form-switch .form-check-input) {
  width: 44px;
  height: 22px;
  border-radius: 11px;
  transition: all 0.3s ease;
  background-image: none;
  position: relative;
}

/* Corrigir a bolinha do switch */
:deep(.form-switch .form-check-input::after) {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 18px;
  height: 18px;
  background-color: #fff;
  border-radius: 50%;
  transition: transform 0.3s ease;
  content: '';
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Posição da bolinha quando ativo */
:deep(.form-switch .form-check-input:checked::after) {
  transform: translateX(22px);
}

:deep(.form-switch .form-check-label) {
  font-size: 14px;
  font-weight: 400;
  margin-left: 8px;
}

/* Adicionar margens laterais no mobile para evitar que o card cole nas bordas */
@media (max-width: 767.98px) {
  .container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Garantir que o card tenha um espaçamento mínimo das bordas */
  .login-card {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }

  .card-body {
    padding: 1.5rem 1.75rem 1.75rem 1.75rem;
  }

  :deep(.form-control) {
    height: 46px;
    font-size: 16px; /* Evita zoom no iOS */
  }

  :deep(.btn) {
    height: 46px;
  }

  /* Ajustar ícones no mobile */
  .input-icon {
    font-size: 15px;
    left: 14px;
  }

  :deep(.input-with-left-icon) {
    padding-left: 44px !important;
  }

  :deep(.input-with-right-icon) {
    padding-right: 44px !important;
  }

  .password-toggle-btn {
    right: 14px;
    font-size: 15px;
  }
}

/* Corrigir cor do switch para azul */
:deep(.form-switch .form-check-input:checked) {
  background-color: #56809F !important;
  border-color: #56809F !important;
  background-image: none !important;
}

/* Remover estilos padrão do Bootstrap que podem interferir */
:deep(.form-switch .form-check-input:focus) {
  background-image: none !important;
  box-shadow: 0 0 0 2px rgba(86, 128, 159, 0.25) !important;
}

/* ===== ESTADOS INICIAIS (ANTES DAS ANIMAÇÕES) ===== */

/* Elementos começam invisíveis até as animações serem ativadas */
.container:not(.animated-container) {
  opacity: 0;
}

.login-card:not(.animated-card) {
  opacity: 0;
  transform: translateY(30px) scale(0.95);
}

.card-header:not(.animated-header) {
  opacity: 0;
  transform: translateY(-20px);
}

.login-page-logo:not(.animated-logo) {
  opacity: 0;
  transform: scale(0.8);
  filter: brightness(0.8);
}

.card-body:not(.animated-body) {
  opacity: 0;
}

.footer:not(.animated-footer) {
  opacity: 0;
  transform: translateY(20px);
}

/* Campos do formulário começam invisíveis */
.mb-3:not(.animated-field),
.text-center:not(.animated-field),
.mt-4:not(.animated-field) {
  opacity: 0;
  transform: translateX(-20px);
}

/* ===== ANIMAÇÕES DE "SISTEMA ACORDANDO" ===== */

/* Animação do background - efeito de despertar */
.animated-mask {
  animation: backgroundAwaken 1.2s ease-out forwards;
}

@keyframes backgroundAwaken {
  0% {
    opacity: 0.9;
    transform: scale(1.02);
  }
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
}

/* Container principal - movimento suave de entrada */
.animated-container {
  animation: containerFloat 1s ease-out forwards;
}

@keyframes containerFloat {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Card principal - entrada elegante com escala */
.animated-card {
  animation: cardAwaken 0.8s ease-out 0.2s forwards;
}

@keyframes cardAwaken {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-5px) scale(1.01);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Header do card - deslizamento suave */
.animated-header {
  animation: headerSlide 0.6s ease-out 0.4s forwards;
  opacity: 0;
  transform: translateY(-20px);
}

@keyframes headerSlide {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Logo - efeito de "despertar" com brilho */
.animated-logo {
  animation: logoAwaken 0.8s ease-out 0.5s forwards;
  opacity: 0;
  transform: scale(0.8);
  filter: brightness(0.8);
}

@keyframes logoAwaken {
  0% {
    opacity: 0;
    transform: scale(0.8);
    filter: brightness(0.8);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
    filter: brightness(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
    filter: brightness(1);
  }
}

/* Corpo do card - fade in suave */
.animated-body {
  animation: bodyFadeIn 0.6s ease-out 0.6s forwards;
  opacity: 0;
}

@keyframes bodyFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Campos do formulário - entrada sequencial */
.animated-field {
  animation: fieldSlideIn 0.5s ease-out forwards;
  opacity: 0;
  transform: translateX(-20px);
}

@keyframes fieldSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Footer - entrada sutil de baixo */
.animated-footer {
  animation: footerRise 0.8s ease-out 0.9s forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes footerRise {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== ANIMAÇÕES DE LOADING (MANTIDAS) ===== */

/* Login submission loading overlay */
.login-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.25);
  z-index: 9998;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s ease-in-out, visibility 0.4s ease-in-out;
  backdrop-filter: blur(1px);
  -webkit-backdrop-filter: blur(1px);
}

.login-loading-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Animações de loading */
.login-card {
  transition: all 0.3s ease;
  position: relative;
  z-index: 10 !important;
}

.login-card.logging-in {
  transform: scale(0.98);
  opacity: 0.9;
}

.login-card.logging-in .card-body {
  pointer-events: none;
}

/* Efeito de pulse no logo durante o loading */
.login-card.logging-in .login-page-logo {
  animation: loginPulse 1.5s ease-in-out infinite;
}

@keyframes loginPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* ===== ANIMAÇÕES DE INTERAÇÃO (SISTEMA VIVO) ===== */

/* Card com hover sutil */
.login-card:not(.logging-in) {
  transition: all 0.3s ease;
}

.login-card:not(.logging-in):hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Logo com hover interativo */
.login-page-logo {
  transition: all 0.3s ease;
  cursor: pointer;
}

.login-page-logo:hover {
  transform: scale(1.02);
  filter: brightness(1.05);
}

/* Links com animação suave */
.animated-footer a,
.card-body a {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.animated-footer a:hover,
.card-body a:hover {
  transform: translateY(-1px);
}

/* Efeito de respiração sutil no background */
.page-header.breathing {
  animation: backgroundBreathe 8s ease-in-out infinite;
}

@keyframes backgroundBreathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.01);
  }
}

/* Animação de foco nos campos */
:deep(.form-floating > .form-control:focus),
:deep(.form-floating > .form-control:not(:placeholder-shown)) {
  transform: translateY(-1px);
  transition: all 0.3s ease;
}

/* Responsividade das animações */
@media (max-width: 768px) {
  .animated-card {
    animation-duration: 0.6s;
  }

  .animated-field {
    animation-duration: 0.4s;
  }

  .page-header {
    animation: none; /* Desabilitar respiração em mobile */
  }
}

/* ===== ESTILOS PARA RECUPERAÇÃO DE SENHA ===== */

/* Link "Esqueceu a senha?" */
.forgot-password-link {
  color: #56809F;
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.forgot-password-link:hover {
  color: #4a6d85;
  text-decoration: none;
}

.forgot-password-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 50%;
  background-color: #56809F;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.forgot-password-link:hover::after {
  width: 100%;
}

/* Formulário de recuperação */
.password-recovery-form {
  animation: slideInFromRight 0.4s ease-out;
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.recovery-header {
  position: relative;
  margin-bottom: 1.5rem;
}

.btn-voltar {
  position: absolute;
  left: 0;
  top: 0;
  background: none;
  border: none;
  color: #6c757d;
  font-size: 1.1rem;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-voltar:hover {
  color: #56809F;
  background-color: rgba(86, 128, 159, 0.1);
  transform: translateX(-2px);
}

.recovery-title {
  color: #2c3e50;
  font-weight: 600;
  margin: 0;
  font-size: 1.25rem;
}

.recovery-subtitle {
  color: #6c757d;
  font-size: 0.9rem;
  margin: 0.5rem 0 0 0;
  line-height: 1.4;
}

.recovery-form {
  animation: fadeInUp 0.5s ease-out 0.1s both;
  position: relative;
  z-index: 100 !important;
}

.password-recovery-form {
  position: relative;
  z-index: 100 !important;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Botão de recuperação - FORÇAR VISIBILIDADE */
.btn-recovery,
button.btn-recovery,
.recovery-form .btn-recovery,
.recovery-form button.btn-recovery {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  background: linear-gradient(135deg, #56809F 0%, #4a6d85 100%) !important;
  color: #ffffff !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  padding: 12px 24px !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 15px rgba(86, 128, 159, 0.3) !important;
  border: none !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
  min-height: 45px !important;
  width: 100% !important;
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}

.btn-recovery:hover:not(:disabled) {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(86, 128, 159, 0.4) !important;
  background: linear-gradient(135deg, #4a6d85 0%, #3d5a6e 100%) !important;
}

.btn-recovery:disabled {
  opacity: 0.7 !important;
  cursor: not-allowed !important;
}

.btn-recovery:active:not(:disabled) {
  transform: translateY(0) !important;
  box-shadow: 0 2px 10px rgba(86, 128, 159, 0.3) !important;
}

/* Mensagem de sucesso */
.alert {
  border-radius: 8px;
  border: none;
  font-size: 0.9rem;
  padding: 1rem;
  margin-top: 1rem;
  animation: slideInFromTop 0.4s ease-out;
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.alert-success,
.recovery-form .alert-success {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
  color: #155724 !important;
  border-left: 4px solid #28a745 !important;
  font-weight: 500 !important;
  padding: 1rem !important;
  margin-top: 1rem !important;
  border-radius: 8px !important;
}

.alert-danger,
.recovery-form .alert-danger {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%) !important;
  color: #721c24 !important;
  border-left: 4px solid #dc3545 !important;
  font-weight: 500 !important;
  padding: 1rem !important;
  margin-top: 1rem !important;
  border-radius: 8px !important;
}

/* ===== NOVOS ESTILOS - RECUPERAÇÃO REIMPLEMENTADA ===== */
.password-recovery-container {
  padding: 1.5rem 0 !important;
  width: 100% !important;
  background: transparent !important;
}

.btn-back-to-login {
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  background: #ffffff !important;
  border: 2px solid #dee2e6 !important;
  color: #6c757d !important;
  padding: 0.6rem 1.2rem !important;
  border-radius: 8px !important;
  font-size: 0.9rem !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
}

.btn-back-to-login:hover {
  background: #f8f9fa !important;
  border-color: #56809F !important;
  color: #56809F !important;
  transform: translateX(-3px) !important;
}

.recovery-title-section {
  text-align: center !important;
  margin: 2rem 0 !important;
}

.recovery-main-title {
  color: #2c3e50 !important;
  font-size: 1.6rem !important;
  font-weight: 700 !important;
  margin: 0 0 0.75rem 0 !important;
}

.recovery-description {
  color: #6c757d !important;
  font-size: 0.95rem !important;
  margin: 0 !important;
  line-height: 1.6 !important;
}

.recovery-form-new {
  width: 100% !important;
  margin-top: 1.5rem !important;
}

.form-group-recovery {
  margin-bottom: 1.5rem !important;
}

.recovery-label {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  color: #495057 !important;
  font-size: 0.95rem !important;
  font-weight: 600 !important;
  margin-bottom: 0.6rem !important;
}

.recovery-label i {
  color: #56809F !important;
  font-size: 0.9rem !important;
}

.recovery-input {
  width: 100% !important;
  padding: 0.85rem 1.1rem !important;
  font-size: 1rem !important;
  border: 2px solid #dee2e6 !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  background: #ffffff !important;
  color: #2c3e50 !important;
  font-family: inherit !important;
}

.recovery-input:focus {
  outline: none !important;
  border-color: #56809F !important;
  box-shadow: 0 0 0 4px rgba(86, 128, 159, 0.15) !important;
}

.recovery-input:disabled {
  background: #f8f9fa !important;
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}

.recovery-input::placeholder {
  color: #adb5bd !important;
}

.btn-send-recovery {
  width: 100% !important;
  padding: 0.95rem 1.5rem !important;
  background: linear-gradient(135deg, #56809F 0%, #4a6d85 100%) !important;
  color: #ffffff !important;
  border: none !important;
  border-radius: 8px !important;
  font-size: 1.05rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(86, 128, 159, 0.3) !important;
  margin-top: 0.5rem !important;
}

.btn-send-recovery:hover:not(:disabled) {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 18px rgba(86, 128, 159, 0.4) !important;
}

.btn-send-recovery:active:not(:disabled) {
  transform: translateY(0) !important;
}

.btn-send-recovery:disabled {
  opacity: 0.7 !important;
  cursor: not-allowed !important;
}

.btn-send-recovery i {
  margin-right: 0.5rem !important;
}

.recovery-alert {
  display: flex !important;
  align-items: flex-start !important;
  gap: 1rem !important;
  padding: 1.2rem !important;
  border-radius: 8px !important;
  margin-top: 1.5rem !important;
  animation: slideInFromTop 0.4s ease-out !important;
}

.recovery-alert i {
  font-size: 1.5rem !important;
  flex-shrink: 0 !important;
  margin-top: 0.1rem !important;
}

.recovery-alert strong {
  display: block !important;
  margin-bottom: 0.25rem !important;
  font-size: 1rem !important;
}

.recovery-alert p {
  margin: 0 !important;
  font-size: 0.9rem !important;
  line-height: 1.5 !important;
}

.recovery-alert-success {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%) !important;
  color: #155724 !important;
  border-left: 4px solid #28a745 !important;
}

.recovery-alert-success i {
  color: #28a745 !important;
}

.recovery-alert-error {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%) !important;
  color: #721c24 !important;
  border-left: 4px solid #dc3545 !important;
}

.recovery-alert-error i {
  color: #dc3545 !important;
}

/* Feedback de erro */
.is-invalid {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1) !important;
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.8rem;
  color: #dc3545;
  font-weight: 500;
}

/* Responsividade para recuperação */
@media (max-width: 767.98px) {
  .recovery-title {
    font-size: 1.1rem;
  }

  .recovery-subtitle {
    font-size: 0.85rem;
  }

  .btn-voltar {
    font-size: 1rem;
    padding: 6px;
  }
}

/* Reduzir animações para usuários que preferem menos movimento */
@media (prefers-reduced-motion: reduce) {
  .animated-mask,
  .animated-container,
  .animated-card,
  .animated-header,
  .animated-logo,
  .animated-body,
  .animated-field,
  .animated-footer,
  .page-header {
    animation: none;
    opacity: 1;
    transform: none;
  }

  .login-card:hover {
    transform: none;
  }

  .password-recovery-form,
  .recovery-form {
    animation: none;
  }
}
</style>

<script>
import MaterialInput from "@/components/MaterialInput.vue";
import MaterialSwitch from "@/components/MaterialSwitch.vue";
import MaterialButton from "@/components/MaterialButton.vue";
import TrialPlanSelectorModal from "@/components/TrialPlanSelectorModal.vue";
import TrialSignupModal from "@/components/TrialSignupModal.vue";
import { mapMutations } from "vuex";
import whiteConsultory from "@/assets/img/lumi/whiteConsultory.jpg";
import LumiBlueLogo from "@/assets/img/lumi/lumi-vision-logo-170.png";
import usuariosService from "@/services/usuariosService.js";
import passwordResetService from "@/services/passwordResetService.js";
import { createTrialAccount } from "@/services/clinicasService.js";
import { openModal, closeModal } from "@/utils/modalHelper.js";
import router from "../router/index.js";
import cSwal from "@/utils/cSwal.js";

const credentials = {
  username: "",
  password: "",
};

export default {
  name: "login",
  components: {
    MaterialInput,
    MaterialSwitch,
    MaterialButton,
    TrialPlanSelectorModal,
    TrialSignupModal,
  },
  data() {
    return {
      credentials,
      LumiBlueLogo,
      rememberDevice: false,
      isLoggingIn: false,
      showAnimations: false, // Controla quando as animações começam
      showPassword: false, // Controla visibilidade da senha
      // Recuperação de senha
      mostrandoRecuperacao: false,
      emailRecuperacao: '',
      enviandoRecuperacao: false,
      mensagemSucesso: '',
      erroEmailRecuperacao: '',
      // Trial signup
      planoSelecionadoParaTrial: null,
    };
  },
  mounted() {
    if (usuariosService.isAuthenticated()) {
      router.push({ path: "inicio" });
      return;
    }

    // Aguardar o loading inicial terminar e então iniciar as animações
    this.waitForInitialLoadingToEnd();
  },
  watch: {
    // Monitorar mudanças no token do Vuex store
    '$store.state.token': {
      handler(newToken) {
        if (newToken && Object.keys(newToken).length > 0) {
          // Token foi definido, redirecionar para dashboard
          setTimeout(() => {
            this.$router.push('/inicio');
          }, 500);
        }
      },
      deep: true
    }
  },
  methods: {
    ...mapMutations(["toggleEveryDisplay", "toggleHideConfig"]),

    // Método para aguardar o loading inicial terminar
    waitForInitialLoadingToEnd() {
      // Verificar se o loading inicial ainda existe
      const checkLoader = () => {
        const initialLoader = document.getElementById('app-initial-loader');
        if (initialLoader && initialLoader.style.display !== 'none' && !initialLoader.classList.contains('fade-out')) {
          // Loading ainda ativo, verificar novamente em 100ms
          setTimeout(checkLoader, 100);
        } else {
          // Loading terminou, aguardar um pouco mais e iniciar animações
          setTimeout(() => {
            this.showAnimations = true;
          }, 300);
        }
      };

      // Fallback: se não encontrar o loader, iniciar animações após um tempo
      setTimeout(() => {
        if (!this.showAnimations) {
          this.showAnimations = true;
        }
      }, 1500);

      // Iniciar verificação
      checkLoader();
    },

    togglePasswordVisibility() {
      this.showPassword = !this.showPassword;
    },

    // Métodos para recuperação de senha
    mostrarFormularioRecuperacao() {
      this.mostrandoRecuperacao = true;
      this.limparFormularioRecuperacao();
    },

    voltarParaLogin() {
      this.mostrandoRecuperacao = false;
      this.limparFormularioRecuperacao();
    },

    limparFormularioRecuperacao() {
      this.emailRecuperacao = '';
      this.mensagemSucesso = '';
      this.erroEmailRecuperacao = '';
      this.enviandoRecuperacao = false;
    },

    async enviarLinkRecuperacao() {
      if (this.enviandoRecuperacao) return;

      // Limpar erros anteriores
      this.erroEmailRecuperacao = '';
      this.mensagemSucesso = '';

      // Validação básica
      if (!this.emailRecuperacao) {
        this.erroEmailRecuperacao = 'Por favor, digite seu e-mail';
        return;
      }

      if (!this.isValidEmail(this.emailRecuperacao)) {
        this.erroEmailRecuperacao = 'Por favor, digite um e-mail válido';
        return;
      }

      this.enviandoRecuperacao = true;

      try {
        const result = await passwordResetService.sendResetLink(this.emailRecuperacao);

        if (result.success) {
          this.mensagemSucesso = result.data.message;
          this.emailRecuperacao = '';

          // Opcional: voltar para login após alguns segundos
          setTimeout(() => {
            this.voltarParaLogin();
          }, 5000);
        } else {
          this.erroEmailRecuperacao = result.error;
        }
      } catch (error) {
        console.error('Erro ao enviar link de recuperação:', error);
        this.erroEmailRecuperacao = 'Erro interno. Tente novamente em alguns minutos.';
      } finally {
        this.enviandoRecuperacao = false;
      }
    },

    isValidEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    },

    async submitLogin() {
      if (this.isLoggingIn) return; // Previne múltiplos submits

      this.isLoggingIn = true;

      try {
        const auth = await usuariosService.login(this.credentials);

        if (auth) {
          // Pequeno delay para mostrar o feedback visual
          setTimeout(() => {
            this.$router.go("/inicio");
          }, 500);
        } else {
          this.isLoggingIn = false;
          cSwal.cError("Usuário ou senha incorretos.");
        }
      } catch (error) {
        this.isLoggingIn = false;
        cSwal.cError("Erro ao fazer login. Tente novamente.");
      }
    },

    // Métodos para fluxo de teste gratuito
    abrirModalTesteGratuito() {
      openModal('trialPlanSelectorModal');
    },

    onPlanoSelecionado(plano) {
      this.planoSelecionadoParaTrial = plano;
      
      // Fechar modal de seleção
      closeModal('trialPlanSelectorModal');
      
      // Abrir modal de cadastro após um pequeno delay
      setTimeout(() => {
        openModal('trialSignupModal');
      }, 300);
    },

    onVoltarParaSelecaoPlano() {
      // Fechar modal de cadastro
      closeModal('trialSignupModal');
      
      // Reabrir modal de seleção após um pequeno delay
      setTimeout(() => {
        openModal('trialPlanSelectorModal');
      }, 300);
    },

    async onTrialSignupSuccess(trialData) {
      cSwal.loading('Criando sua conta de teste...');
      
      try {
        const response = await createTrialAccount(trialData);
        
        if (response.success) {
          // Fechar modal de cadastro
          closeModal('trialSignupModal');
          
          cSwal.loaded();
          cSwal.cSuccess('Conta criada com sucesso! Fazendo login...');
          
          // Login automático
          await usuariosService.login({
            username: trialData.dentista.email,
            password: trialData.dentista.senha
          });
          
          // Redirecionar para dashboard
          setTimeout(() => {
            this.$router.push('/inicio');
          }, 1000);
          
        } else {
          throw new Error(response.message || 'Erro ao criar conta trial');
        }
        
      } catch (error) {
        cSwal.loaded();
        console.error('Erro no signup trial:', error);
        cSwal.cError(error.message || 'Erro ao criar conta de teste. Tente novamente.');
      }
    },

    onTrialSignupError(error) {
      console.error('Erro no trial signup:', error);
      cSwal.cError(error.message || 'Erro ao criar conta de teste.');
    },
  },
  computed: {
    backgroundImage() {
      return {
        backgroundImage: `url(${whiteConsultory})`,
        transform: "scale(1.05)",
      };
    },
  },
  beforeMount() {
    this.toggleEveryDisplay();
    this.toggleHideConfig();
  },
  beforeUnmount() {
    this.toggleEveryDisplay();
    this.toggleHideConfig();
  },
};
</script>
