<template>
  <div class="assinaturas-clinica">
    <!-- Header com estatísticas -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header pb-0">
            <div class="d-flex justify-content-between align-items-center">
              <h6 class="mb-0">
                <i class="fas fa-layer-group text-primary me-2"></i>
                Assinaturas da Clínica
              </h6>
              <button
                class="btn btn-primary btn-sm"
                @click="abrirModalNovaAssinatura"
              >
                <i class="fas fa-plus me-1"></i>
                Nova Assinatura
              </button>
            </div>
          </div>
          <div class="card-body">
            <!-- Estatísticas rápidas -->
            <div class="row g-3 mb-3">
              <div class="col-md-3">
                <div class="stat-card stat-card--success">
                  <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ estatisticas.ativas || 0 }}</div>
                    <div class="stat-label">Ativas</div>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="stat-card stat-card--warning">
                  <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ estatisticas.trial || 0 }}</div>
                    <div class="stat-label">Trial</div>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="stat-card stat-card--danger">
                  <div class="stat-icon">
                    <i class="fas fa-times-circle"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ estatisticas.canceladas || 0 }}</div>
                    <div class="stat-label">Canceladas</div>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="stat-card stat-card--info">
                  <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ formatCurrency(estatisticas.valor_total || 0) }}</div>
                    <div class="stat-label">Valor Total</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filtros -->
    <div class="row mb-3">
      <div class="col-md-4">
        <div class="form-group">
          <label class="form-label">Status</label>
          <select class="form-select" v-model="filtros.status" @change="loadAssinaturas">
            <option value="">Todos os status</option>
            <option value="ativo">Ativo</option>
            <option value="trial">Trial</option>
            <option value="cancelado">Cancelado</option>
            <option value="suspenso">Suspenso</option>
          </select>
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group">
          <label class="form-label">Período</label>
          <select class="form-select" v-model="filtros.periodo" @change="loadAssinaturas">
            <option value="">Todos os períodos</option>
            <option value="mensal">Mensal</option>
            <option value="trimestral">Trimestral</option>
            <option value="semestral">Semestral</option>
            <option value="anual">Anual</option>
          </select>
        </div>
      </div>
      <div class="col-md-4">
        <div class="form-group">
          <label class="form-label">Vigência</label>
          <select class="form-select" v-model="filtros.vigencia" @change="loadAssinaturas">
            <option value="">Todas</option>
            <option value="vigente">Vigentes</option>
            <option value="vencida">Vencidas</option>
            <option value="futura">Futuras</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Lista de Assinaturas -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header pb-0">
            <h6 class="mb-0">Histórico de Períodos de Assinatura</h6>
          </div>
          <div class="card-body p-0">
            <!-- Tabela de assinaturas -->
            <div class="table-responsive">
              <table class="table align-items-center mb-0">
                <thead>
                  <tr>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Plano
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Período
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Duração
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Valor
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Status
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-if="isLoading">
                    <td colspan="6" class="text-center py-4">
                      <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                      </div>
                    </td>
                  </tr>
                  <tr v-else-if="filteredAssinaturas.length === 0">
                    <td colspan="6" class="text-center py-4 text-muted">
                      {{ assinaturas.length === 0 ? 'Nenhuma assinatura encontrada para esta clínica' : 'Nenhuma assinatura corresponde aos filtros aplicados' }}
                    </td>
                  </tr>
                  <tr v-else v-for="assinatura in filteredAssinaturas" :key="assinatura.id">
                    <td>
                      <div class="d-flex px-2 py-1">
                        <div
                          class="plan-color-indicator me-2"
                          :style="{ backgroundColor: assinatura.plano?.cor || '#6c757d' }"
                        ></div>
                        <div class="d-flex flex-column justify-content-center">
                          <h6 class="mb-0 text-sm">{{ assinatura.plano?.nome || 'Plano não encontrado' }}</h6>
                          <p class="text-xs text-secondary mb-0">ID: {{ assinatura.id }}</p>
                        </div>
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">
                        {{ formatDateRange(assinatura.data_inicio, assinatura.data_fim) }}
                      </span>
                      <div class="text-xs text-muted">
                        {{ assinatura.is_periodo_atual ? 'Período Atual' : 'Período Encerrado' }}
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">
                        {{ assinatura.duracao_dias }} dias
                      </span>
                      <div class="text-xs text-muted">
                        {{ Math.ceil(assinatura.duracao_dias / 30) }} mês(es)
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">
                        {{ formatCurrency(assinatura.valor_mensal) }}
                      </span>
                      <div class="text-xs text-muted">/mês</div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="badge badge-sm" :class="getStatusBadgeClass(assinatura.status)">
                        {{ formatStatus(assinatura.status) }}
                      </span>
                    </td>
                    <td class="align-middle text-center">
                      <div class="btn-group btn-group-sm">
                        <button
                          class="btn btn-outline-primary btn-sm"
                          @click="visualizarAssinatura(assinatura)"
                          title="Visualizar detalhes"
                        >
                          <i class="fas fa-eye"></i>
                        </button>
                        <button
                          v-if="assinatura.is_periodo_atual"
                          class="btn btn-outline-danger btn-sm"
                          @click="cancelarAssinatura(assinatura)"
                          title="Cancelar período atual"
                        >
                          <i class="fas fa-times"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Paginação -->
            <div v-if="pagination.total > pagination.per_page" class="d-flex justify-content-center mt-3">
              <nav>
                <ul class="pagination pagination-sm">
                  <li class="page-item" :class="{ disabled: pagination.current_page === 1 }">
                    <button class="page-link" @click="changePage(pagination.current_page - 1)">
                      <i class="fas fa-chevron-left"></i>
                    </button>
                  </li>
                  <li 
                    v-for="page in visiblePages" 
                    :key="page"
                    class="page-item" 
                    :class="{ active: page === pagination.current_page }"
                  >
                    <button class="page-link" @click="changePage(page)">{{ page }}</button>
                  </li>
                  <li class="page-item" :class="{ disabled: pagination.current_page === pagination.last_page }">
                    <button class="page-link" @click="changePage(pagination.current_page + 1)">
                      <i class="fas fa-chevron-right"></i>
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { assinaturasService } from '@/services/assinaturasService';
import cSwal from '@/utils/cSwal';
import { openModal } from '@/utils/modalHelper';

export default {
  name: 'AssinaturasClinica',
  props: {
    clinica: {
      type: Object,
      required: true
    }
  },
  emits: ['assinatura-alterada'],
  data() {
    return {
      assinaturas: [],
      estatisticas: {},
      isLoading: false,
      filtros: {
        status: '',
        periodo: '',
        vigencia: ''
      },
      pagination: {
        current_page: 1,
        last_page: 1,
        per_page: 10,
        total: 0
      }
    };
  },
  computed: {
    filteredAssinaturas() {
      if (!Array.isArray(this.assinaturas)) {
        return [];
      }
      let filtered = [...this.assinaturas];

      // Filtrar por status
      if (this.filtros.status && this.filtros.status !== 'todos') {
        filtered = filtered.filter(assinatura => assinatura.status === this.filtros.status);
      }

      // Filtrar por período de cobrança
      if (this.filtros.periodo && this.filtros.periodo !== 'todos') {
        filtered = filtered.filter(assinatura => assinatura.periodo_cobranca === this.filtros.periodo);
      }

      // Filtrar por vigência
      if (this.filtros.vigencia && this.filtros.vigencia !== '') {
        const hoje = new Date();
        filtered = filtered.filter(assinatura => {
          const dataInicio = new Date(assinatura.data_inicio);
          const dataFim = new Date(assinatura.data_fim);

          switch (this.filtros.vigencia) {
            case 'vigente':
              return dataInicio <= hoje && dataFim >= hoje;
            case 'vencida':
              return dataFim < hoje;
            case 'futura':
              return dataInicio > hoje;
            default:
              return true;
          }
        });
      }

      return filtered;
    },

    visiblePages() {
      const current = this.pagination.current_page;
      const last = this.pagination.last_page;
      const delta = 2;
      const range = [];
      
      for (let i = Math.max(2, current - delta); i <= Math.min(last - 1, current + delta); i++) {
        range.push(i);
      }
      
      if (current - delta > 2) {
        range.unshift('...');
      }
      if (current + delta < last - 1) {
        range.push('...');
      }
      
      range.unshift(1);
      if (last !== 1) {
        range.push(last);
      }
      
      return range;
    }
  },
  async mounted() {
    await this.loadAssinaturas();
    await this.loadEstatisticas();
  },
  methods: {
    async loadAssinaturas() {
      this.isLoading = true;
      try {
        const params = {
          page: this.pagination.current_page,
          per_page: this.pagination.per_page,
          ...this.filtros
        };
        
        const response = await assinaturasService.getAssinaturas(this.clinica.id, params);
        // O backend retorna um array diretamente, não um objeto com data/pagination
        this.assinaturas = Array.isArray(response) ? response : (response.data || []);
        this.pagination = response.pagination || {
          current_page: 1,
          last_page: 1,
          total: this.assinaturas.length
        };
      } catch (error) {
        console.error('Erro ao carregar assinaturas:', error);
        cSwal.cError('Erro ao carregar assinaturas.');
      }
      this.isLoading = false;
    },
    
    async loadEstatisticas() {
      try {
        this.estatisticas = await assinaturasService.getEstatisticas(this.clinica.id);
      } catch (error) {
        console.error('Erro ao carregar estatísticas:', error);
      }
    },
    
    changePage(page) {
      if (page >= 1 && page <= this.pagination.last_page) {
        this.pagination.current_page = page;
        this.loadAssinaturas();
      }
    },
    
    formatCurrency(value) {
      return assinaturasService.formatCurrency(value);
    },
    
    formatStatus(status) {
      return assinaturasService.formatStatus(status).text;
    },
    
    formatPeriodoCobranca(periodo) {
      return assinaturasService.formatPeriodoCobranca(periodo);
    },
    
    formatDateRange(inicio, fim) {
      const formatDate = (date) => new Date(date).toLocaleDateString('pt-BR');
      return `${formatDate(inicio)} - ${formatDate(fim)}`;
    },
    
    getStatusBadgeClass(status) {
      const statusInfo = assinaturasService.formatStatus(status);
      return `bg-${statusInfo.color}`;
    },
    
    getVigenciaClass(assinatura) {
      if (assinaturasService.isVigente(assinatura)) {
        return 'text-success';
      } else if (new Date(assinatura.data_inicio) > new Date()) {
        return 'text-info';
      } else {
        return 'text-danger';
      }
    },
    
    getVigenciaText(assinatura) {
      if (assinaturasService.isVigente(assinatura)) {
        const dias = assinaturasService.calcularDiasRestantes(assinatura.data_fim);
        return `${dias} dias restantes`;
      } else if (new Date(assinatura.data_inicio) > new Date()) {
        return 'Futura';
      } else {
        return 'Vencida';
      }
    },
    
    podeSerCancelada(assinatura) {
      return assinaturasService.podeSerCancelada(assinatura);
    },
    
    abrirModalNovaAssinatura() {
      // Abrir modal de nova assinatura
      openModal('novaAssinaturaModal');
    },

    visualizarAssinatura(assinatura) {
      // Implementar modal de detalhes da assinatura
      console.log('Visualizar assinatura:', assinatura);
    },
    
    async cancelarAssinatura(assinatura) {
      const confirmText = `
        <div class="text-start">
          <p><strong>Plano:</strong> ${assinatura.plano?.nome}</p>
          <p><strong>Período:</strong> ${this.formatDateRange(assinatura.data_inicio, assinatura.data_fim)}</p>
          <p class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i> Esta ação encerrará o período atual da assinatura.</p>
        </div>
      `;

      cSwal.cConfirm(
        'Cancelar período atual?',
        async () => {
          await this.processarCancelamento(assinatura);
        },
        'Sim, cancelar',
        'Não cancelar',
        confirmText
      );
    },
    
    async processarCancelamento(assinatura) {
      // Solicitar data de cancelamento e motivo
      const { value: formValues } = await cSwal.fire({
        title: 'Dados do Cancelamento',
        html: `
          <div class="text-start">
            <div class="mb-3">
              <label for="data_cancelamento" class="form-label">Data do Cancelamento</label>
              <input type="date" id="data_cancelamento" class="form-control" value="${new Date().toISOString().split('T')[0]}">
            </div>
            <div class="mb-3">
              <label for="motivo_cancelamento" class="form-label">Motivo do Cancelamento</label>
              <textarea id="motivo_cancelamento" class="form-control" rows="3" placeholder="Descreva o motivo do cancelamento..."></textarea>
            </div>
          </div>
        `,
        focusConfirm: false,
        showCancelButton: true,
        confirmButtonText: 'Cancelar Assinatura',
        cancelButtonText: 'Voltar',
        preConfirm: () => {
          const dataCancelamento = document.getElementById('data_cancelamento').value;
          const motivoCancelamento = document.getElementById('motivo_cancelamento').value;

          if (!dataCancelamento) {
            cSwal.showValidationMessage('Data de cancelamento é obrigatória');
            return false;
          }

          if (!motivoCancelamento.trim()) {
            cSwal.showValidationMessage('Motivo do cancelamento é obrigatório');
            return false;
          }

          return {
            data_cancelamento: dataCancelamento,
            motivo_cancelamento: motivoCancelamento.trim()
          };
        }
      });

      if (!formValues) return;

      cSwal.loading('Cancelando assinatura...');

      try {
        await assinaturasService.cancelarAssinatura(this.clinica.id, formValues);

        cSwal.loaded();
        cSwal.cSuccess('Assinatura cancelada com sucesso!');

        // Recarregar dados
        await this.loadAssinaturas();
        await this.loadEstatisticas();

        // Emitir evento para atualizar dados na tela pai
        this.$emit('assinatura-alterada', {
          tipo: 'cancelamento',
          assinatura: assinatura
        });

      } catch (error) {
        cSwal.loaded();
        console.error('Erro ao cancelar assinatura:', error);

        if (error.response?.status === 422) {
          const message = error.response.data.message || 'Erro de validação';
          cSwal.cError(message);
        } else {
          cSwal.cError('Erro ao cancelar assinatura. Tente novamente.');
        }
      }
    }
  }
};
</script>

<style scoped>
.stat-card {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  background: white;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card--success { border-left: 4px solid #28a745; }
.stat-card--warning { border-left: 4px solid #ffc107; }
.stat-card--danger { border-left: 4px solid #dc3545; }
.stat-card--info { border-left: 4px solid #17a2b8; }

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  margin-right: 1rem;
  font-size: 1.25rem;
}

.stat-card--success .stat-icon {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.stat-card--warning .stat-icon {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.stat-card--danger .stat-icon {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.stat-card--info .stat-icon {
  background: rgba(23, 162, 184, 0.1);
  color: #17a2b8;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #718096;
  font-weight: 500;
}

.plan-color-indicator {
  width: 4px;
  height: 2rem;
  border-radius: 2px;
}

.empty-state {
  padding: 2rem;
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.table th {
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.vigencia-info,
.valor-info {
  line-height: 1.2;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
}

.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}
</style>
