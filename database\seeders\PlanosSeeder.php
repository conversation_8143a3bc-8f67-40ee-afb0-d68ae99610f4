<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Plano;
use App\Models\Clinica;

class PlanosSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Criar planos padrão
        $planos = [
            [
                'nome' => 'Básico',
                'descricao' => 'Plano básico para clínicas pequenas com funcionalidades essenciais',
                'cor' => '#28a745',
                'ativo' => true,
                'modulo_clinica' => true,
                'modulo_ortodontia' => false,
                'quantidade_usuarios' => 3,
                'quantidade_ortodontistas' => 1,
                'quantidade_agendas' => 2,
                'quantidade_cadeiras' => 3,
                'meses_fidelidade_minima' => 12,
                'quantidade_mentorias_mensais' => null,
                'valor_mensal' => 199.90,
            ],
            [
                'nome' => 'Profissional',
                'descricao' => 'Plano completo com módulo de ortodontia e mentorias',
                'cor' => '#007bff',
                'ativo' => true,
                'modulo_clinica' => true,
                'modulo_ortodontia' => true,
                'quantidade_usuarios' => 10,
                'quantidade_ortodontistas' => 3,
                'quantidade_agendas' => 5,
                'quantidade_cadeiras' => 8,
                'meses_fidelidade_minima' => 12,
                'quantidade_mentorias_mensais' => 5,
                'valor_mensal' => 399.90,
            ],
            [
                'nome' => 'Enterprise',
                'descricao' => 'Plano premium para grandes clínicas com recursos ilimitados',
                'cor' => '#6f42c1',
                'ativo' => true,
                'modulo_clinica' => true,
                'modulo_ortodontia' => true,
                'quantidade_usuarios' => null, // Ilimitado
                'quantidade_ortodontistas' => null, // Ilimitado
                'quantidade_agendas' => null, // Ilimitado
                'quantidade_cadeiras' => null, // Ilimitado
                'meses_fidelidade_minima' => 24,
                'quantidade_mentorias_mensais' => null, // Ilimitado
                'valor_mensal' => 799.90,
            ],
            [
                'nome' => 'Teste Gratuito',
                'descricao' => 'Plano gratuito para teste da plataforma por 30 dias',
                'cor' => '#ffc107',
                'ativo' => true,
                'modulo_clinica' => true,
                'modulo_ortodontia' => false,
                'quantidade_usuarios' => 2,
                'quantidade_ortodontistas' => 1,
                'quantidade_agendas' => 1,
                'quantidade_cadeiras' => 2,
                'meses_fidelidade_minima' => 0,
                'quantidade_mentorias_mensais' => null,
                'valor_mensal' => null, // Gratuito
            ],
            [
                'nome' => 'Ortodontia Especializada',
                'descricao' => 'Plano focado em ortodontia com mentorias especializadas',
                'cor' => '#20c997',
                'ativo' => true,
                'modulo_clinica' => true,
                'modulo_ortodontia' => true,
                'quantidade_usuarios' => 5,
                'quantidade_ortodontistas' => 5,
                'quantidade_agendas' => 3,
                'quantidade_cadeiras' => 5,
                'meses_fidelidade_minima' => 18,
                'quantidade_mentorias_mensais' => 10,
                'valor_mensal' => 599.90,
            ]
        ];

        foreach ($planos as $planoData) {
            Plano::firstOrCreate(
                ['nome' => $planoData['nome']],
                $planoData
            );
        }

        // Associar clínicas existentes a planos
        $this->associarClinicasAPlanos();
    }

    /**
     * Associar clínicas existentes a planos baseado em critérios
     */
    private function associarClinicasAPlanos(): void
    {
        $clinicas = Clinica::whereNull('plano_id')->get();
        
        if ($clinicas->isEmpty()) {
            return;
        }

        $planoBásico = Plano::where('nome', 'Básico')->first();
        $planoProfissional = Plano::where('nome', 'Profissional')->first();
        $planoEnterprise = Plano::where('nome', 'Enterprise')->first();
        $planoTeste = Plano::where('nome', 'Teste Gratuito')->first();

        foreach ($clinicas as $clinica) {
            // Contar usuários, ortodontistas, etc. da clínica para determinar o plano adequado
            $usuariosCount = $clinica->users()->count();
            $ortodontistasCount = $clinica->dentistas()->count();
            
            // Lógica para determinar o plano baseado no tamanho da clínica
            if ($usuariosCount === 0 && $ortodontistasCount === 0) {
                // Clínica nova ou vazia - plano teste
                $planoId = $planoTeste?->id;
            } elseif ($usuariosCount <= 3 && $ortodontistasCount <= 2) {
                // Clínica pequena - plano básico
                $planoId = $planoBásico?->id;
            } elseif ($usuariosCount <= 10 && $ortodontistasCount <= 5) {
                // Clínica média - plano profissional
                $planoId = $planoProfissional?->id;
            } else {
                // Clínica grande - plano enterprise
                $planoId = $planoEnterprise?->id;
            }

            if ($planoId) {
                $clinica->update(['plano_id' => $planoId]);
                
                $this->command->info("Clínica '{$clinica->nome}' associada ao plano ID: {$planoId}");
            }
        }
    }
}
