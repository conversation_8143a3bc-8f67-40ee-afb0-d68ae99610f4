<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Dentista;
use App\Models\MatriculaCounter;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Ty<PERSON>\JWTAuth\Facades\JWTAuth;

class UserManagementController extends Controller
{
    private function getAuthenticatedClinicaId()
    {
        $user = JWTAuth::parseToken()->authenticate();
        return $user->clinica_id;
    }

    private function isClinicaAdmin()
    {
        $user = JWTAuth::parseToken()->authenticate();
        return $user->clinica_admin || $user->system_admin;
    }

    public function index($clinicaId, Request $request)
    {
        $authenticatedClinicaId = $this->getAuthenticatedClinicaId();
        $user = JWTAuth::parseToken()->authenticate();

        if (!$user->system_admin && $authenticatedClinicaId != $clinicaId) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        if (!$user->system_admin && !$user->clinica_admin) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $query = User::where('clinica_id', $clinicaId)->whereNull('deleted_at');

        if ($request->has('search')) {
            $search = $request->input('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('username', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $perPage = $request->input('per_page', 15);
        $users = $query->paginate($perPage);

        return response()->json($users);
    }

    public function show($clinicaId, $usuarioId)
    {
        $user = JWTAuth::parseToken()->authenticate();
        $authenticatedClinicaId = $this->getAuthenticatedClinicaId();

        if (!$user->system_admin && $authenticatedClinicaId != $clinicaId) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        if (!$user->system_admin && !$user->clinica_admin) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $usuario = User::where('clinica_id', $clinicaId)
            ->where('id', $usuarioId)
            ->whereNull('deleted_at')
            ->first();

        if (!$usuario) {
            return response()->json(['message' => 'User not found'], 404);
        }

        return response()->json($usuario);
    }

    public function store($clinicaId, Request $request)
    {
        $user = JWTAuth::parseToken()->authenticate();
        $authenticatedClinicaId = $this->getAuthenticatedClinicaId();

        if (!$user->system_admin && $authenticatedClinicaId != $clinicaId) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        if (!$user->system_admin && !$user->clinica_admin) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users',
            'password' => 'required|string|min:8',
            'language' => 'nullable|string|in:pt,en,es',
            'clinica_admin' => 'nullable|boolean',
            'user_type' => 'nullable|string|in:colaborador,profissional'
        ]);

        // Username será preenchido com o email
        $validated['username'] = $validated['email'];
        $validated['clinica_id'] = $clinicaId;
        $validated['password'] = Hash::make($validated['password']);
        $validated['created_by'] = $user->id;
        $validated['clinica_admin'] = $validated['clinica_admin'] ?? false;

        // Remover user_type do validated pois não é coluna da tabela
        $userType = $validated['user_type'] ?? 'colaborador';
        unset($validated['user_type']);

        $newUser = User::create($validated);

        // Se for profissional da saúde, criar também um Dentista
        if ($userType === 'profissional') {
            $idMatricula = MatriculaCounter::getNextIdMatricula($clinicaId);
            
            Dentista::create([
                'user_id' => $newUser->id,
                'clinica_id' => $clinicaId,
                'nome' => $newUser->name,
                'id_matricula' => $idMatricula,
                'ativo' => true
            ]);
        }

        return response()->json($newUser, 201);
    }

    public function update($clinicaId, $usuarioId, Request $request)
    {
        $user = JWTAuth::parseToken()->authenticate();
        $authenticatedClinicaId = $this->getAuthenticatedClinicaId();

        if (!$user->system_admin && $authenticatedClinicaId != $clinicaId) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        if (!$user->system_admin && !$user->clinica_admin) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $usuario = User::where('clinica_id', $clinicaId)
            ->where('id', $usuarioId)
            ->whereNull('deleted_at')
            ->first();

        if (!$usuario) {
            return response()->json(['message' => 'User not found'], 404);
        }

        if ($user->id == $usuarioId && $request->has('clinica_admin')) {
            return response()->json(['message' => 'Cannot change your own admin permission'], 403);
        }

        $validated = $request->validate([
            'name' => 'nullable|string|max:255',
            'username' => 'nullable|string|max:255|unique:users,username,' . $usuarioId,
            'email' => 'nullable|email|unique:users,email,' . $usuarioId,
            'password' => 'nullable|string|min:8',
            'language' => 'nullable|string|in:pt,en,es',
            'clinica_admin' => 'nullable|boolean'
        ]);

        if (isset($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        }

        $usuario->update($validated);

        return response()->json($usuario);
    }

    public function destroy($clinicaId, $usuarioId)
    {
        $user = JWTAuth::parseToken()->authenticate();
        $authenticatedClinicaId = $this->getAuthenticatedClinicaId();

        if (!$user->system_admin && $authenticatedClinicaId != $clinicaId) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        if (!$user->system_admin && !$user->clinica_admin) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $usuario = User::where('clinica_id', $clinicaId)
            ->where('id', $usuarioId)
            ->whereNull('deleted_at')
            ->first();

        if (!$usuario) {
            return response()->json(['message' => 'User not found'], 404);
        }

        if ($user->id == $usuarioId) {
            return response()->json(['message' => 'Cannot delete your own account'], 403);
        }

        $usuario->delete();

        return response()->json(['message' => 'User deleted successfully']);
    }
}
