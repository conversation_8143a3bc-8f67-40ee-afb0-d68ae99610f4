<template>
  <div class="page-width-container">
    <main class="page-width">
      <div class="container-fluid p-0">
        <div class="card no-shadow">
          <div class="card-body p-3">
            <div class="row gx-4 align-items-center flex-wrap">
              <div class="col-12 col-md-5 col-lg-6 d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                  <div class="avatar avatar-xl position-relative">
                    <div class="profile-pic">
                      <div v-if="isLoading.clinica" class="spinner-border text-primary" role="status"></div>
                      <div v-else class="shadow-sm border-radius-lg d-flex align-center justify-content-center px-2 pb-3"
                        style="border: 2px solid #deeaf2; font-size: 25pt;color: #5988A8;">
                        <v-icon class="d-none d-md-block">mdi-hospital-building</v-icon>
                      </div>
                    </div>
                  </div>
                  <div class="ms-3 clinic-info">
                    <h5 class="mb-1 fs-4 clinic-name">
                      {{ clinica.nome }}
                    </h5>
                    <p class="mb-0 font-weight-bold text-muted">{{ clinica.endereco }}</p>
                  </div>
                </div>
              </div>

              <div class="col-12 col-md-7 col-lg-6 mt-3 mt-md-0 p-0">
                <!-- Menu de navegação -->
                <div class="nav-wrapper position-relative end-0 px-md-2 px-sm-0">
                  <ul class="p-1 bg-transparent nav nav-pills nav-fill menu-2x2" role="tablist">
                    <li class="nav-item" @click="activeTab = 'detalhes'">
                      <a
                        class="px-0 py-1 mb-0 nav-link nav-tab d-flex flex-column align-items-center justify-content-center"
                        :class="{ active: activeTab === 'detalhes' }"
                        href="javascript:;"
                        role="tab"
                        :aria-selected="activeTab === 'detalhes' ? 'true' : 'false'"
                      >
                        <i class="fas fa-list"></i>
                        <span class="mt-1">Detalhes</span>
                      </a>
                    </li>
                    <li class="nav-item" @click="activeTab = 'faturas'">
                      <a
                        class="px-0 py-1 mb-0 nav-link nav-tab d-flex flex-column align-items-center justify-content-center"
                        :class="{ active: activeTab === 'faturas' }"
                        href="javascript:;"
                        role="tab"
                        :aria-selected="activeTab === 'faturas' ? 'true' : 'false'"
                      >
                        <i class="fas fa-file-invoice-dollar"></i>
                        <span class="mt-1">Faturas</span>
                      </a>
                    </li>
                    <li class="nav-item" @click="activeTab = 'assinaturas'">
                      <a
                        class="px-0 py-1 mb-0 nav-link nav-tab d-flex flex-column align-items-center justify-content-center"
                        :class="{ active: activeTab === 'assinaturas' }"
                        href="javascript:;"
                        role="tab"
                        :aria-selected="activeTab === 'assinaturas' ? 'true' : 'false'"
                      >
                        <i class="fas fa-layer-group"></i>
                        <span class="mt-1">Assinaturas</span>
                      </a>
                    </li>

                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tab Content -->
      <div class="container-fluid py-4">
        <div class="row">
          <div class="col-12">
            <!-- Conteúdo da aba Detalhes -->
            <div v-if="activeTab === 'detalhes'" class="clinic-content">
                <!-- Blocos de informações estilo Paciente.vue -->
                <div class="row g-0 mb-4">
                  <!-- Coluna esquerda -->
                  <div class="col-md-6 border-end-md px-4">
                    <!-- Informações da Clínica -->
                    <div class="section-header">
                      <div class="section-header-content">
                        <div class="section-icon">
                          <v-icon>mdi-hospital-building</v-icon>
                        </div>
                        <p class="text-uppercase mb-0">Informações da clínica</p>
                      </div>
                    </div>

                    <div class="clinic-info-block">
                      <div class="info-row editable-row" @mouseenter="showEditIcon('nome')" @mouseleave="hideEditIcon('nome')">
                        <span class="info-label">Nome:</span>
                        <div class="info-value-container">
                          <!-- Modo de visualização -->
                          <span v-if="!isEditingNome" class="info-value">{{ clinica.nome || 'Não informado' }}</span>
                          <span
                            v-if="!isEditingNome"
                            v-show="hoveredField === 'nome'"
                            class="edit-icon-small"
                            @click="editField('nome')"
                            title="Editar nome"
                          >
                            <v-icon>mdi-pencil</v-icon>
                          </span>

                          <!-- Modo de edição -->
                          <div v-if="isEditingNome" class="edit-field-container">
                            <MaterialInput
                              v-model="tempNome"
                              type="text"
                              class="edit-field-input"
                            />
                            <div class="edit-field-actions">
                              <button class="btn btn-sm btn-success me-2" @click="saveField('nome')">
                                <v-icon size="small">mdi-check</v-icon>
                              </button>
                              <button class="btn btn-sm btn-secondary" @click="cancelEditField('nome')">
                                <v-icon size="small">mdi-close</v-icon>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="info-row editable-row" @mouseenter="showEditIcon('slug')" @mouseleave="hideEditIcon('slug')">
                        <span class="info-label">Slug:</span>
                        <div class="info-value-container">
                          <!-- Modo de visualização -->
                          <span v-if="!isEditingSlug" class="info-value">{{ clinica.slug || 'Não informado' }}</span>
                          <span
                            v-if="!isEditingSlug"
                            v-show="hoveredField === 'slug'"
                            class="edit-icon-small"
                            @click="editField('slug')"
                            title="Editar slug"
                          >
                            <v-icon>mdi-pencil</v-icon>
                          </span>

                          <!-- Modo de edição -->
                          <div v-if="isEditingSlug" class="edit-field-container">
                            <MaterialInput
                              v-model="tempSlug"
                              type="text"
                              class="edit-field-input"
                            />
                            <div class="edit-field-actions">
                              <button class="btn btn-sm btn-success me-2" @click="saveField('slug')">
                                <v-icon size="small">mdi-check</v-icon>
                              </button>
                              <button class="btn btn-sm btn-secondary" @click="cancelEditField('slug')">
                                <v-icon size="small">mdi-close</v-icon>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="info-row editable-row" @mouseenter="showEditIcon('observacoes')" @mouseleave="hideEditIcon('observacoes')">
                        <span class="info-label">Observações:</span>
                        <div class="info-value-container">
                          <!-- Modo de visualização -->
                          <span v-if="!isEditingObservacoes" class="info-value">{{ clinica.observacoes || 'Nenhuma observação' }}</span>
                          <span
                            v-if="!isEditingObservacoes"
                            v-show="hoveredField === 'observacoes'"
                            class="edit-icon-small"
                            @click="editField('observacoes')"
                            title="Editar observações"
                          >
                            <v-icon>mdi-pencil</v-icon>
                          </span>

                          <!-- Modo de edição -->
                          <div v-if="isEditingObservacoes" class="edit-field-container">
                            <MaterialInput
                              v-model="tempObservacoes"
                              type="textarea"
                              class="edit-field-input"
                            />
                            <div class="edit-field-actions">
                              <button class="btn btn-sm btn-success me-2" @click="saveField('observacoes')">
                                <v-icon size="small">mdi-check</v-icon>
                              </button>
                              <button class="btn btn-sm btn-secondary" @click="cancelEditField('observacoes')">
                                <v-icon size="small">mdi-close</v-icon>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="info-row">
                        <span class="info-label">Cadastrado em:</span>
                        <span class="info-value">{{ $filters.dateTime(clinica.created_at) }}</span>
                      </div>
                    </div>

                    <!-- Endereço -->
                      <div class="p-horizontal-divider mb-0 w-100"></div>
                      <div class="section-header">
                        <div class="section-header-content">
                          <div class="section-icon">
                            <v-icon>mdi-map-marker</v-icon>
                          </div>
                          <p class="text-uppercase mb-0">Endereço</p>
                        </div>
                        <div class="section-actions">
                          <span
                            v-if="!isEditingEndereco"
                            class="edit-icon-wrapper-light"
                            title="Editar endereço"
                            @click="toggleEditEndereco">
                            <v-icon class="edit-icon-light">mdi-pencil</v-icon>
                          </span>
                          <button
                            v-if="isEditingEndereco"
                            class="btn-cancel-edit"
                            @click="cancelEditEndereco"
                          >
                            <v-icon>mdi-close</v-icon>
                            Cancelar edição
                          </button>
                        </div>
                      </div>

                    <ClinicaAddressInfo
                      ref="clinicaAddressInfo"
                      :clinica="clinica"
                      :isEditing="isEditingEndereco"
                      :isMobile="false"
                      @get-endereco="getEndereco"
                      @update:field="updateClinicaField"
                    />

                    <!-- Botão de salvar endereço -->
                    <div v-if="isEditingEndereco" class="text-end mt-3">
                      <button
                        class="btn btn-success btn-sm"
                        @click="saveEditEndereco"
                      >
                        <v-icon size="small" class="me-1">mdi-check</v-icon>
                        Salvar endereço
                      </button>
                    </div>
                  </div>

                  <!-- Coluna direita -->
                  <div class="col-12 col-md-6 px-4">
                    <!-- Plano -->
                    <hr class="horizontal dark" />
                    <div class="section-header">
                      <div class="section-header-content">
                        <div class="section-icon">
                          <v-icon>mdi-crown</v-icon>
                        </div>
                        <p class="text-uppercase mb-0">Plano & Assinatura</p>
                      </div>
                      <div class="section-actions">
                        <button
                          class="btn btn-outline-primary btn-sm"
                          @click="abrirModalSeletorPlano"
                          title="Alterar plano"
                        >
                          <i class="fas fa-edit me-1"></i>
                          Alterar
                        </button>
                      </div>
                    </div>

                    <div class="clinic-info-block">
                      <div v-if="isLoading.clinica" class="text-center py-3">
                        <div class="spinner-border text-primary" role="status"></div>
                        <p class="mt-2 text-muted">Carregando informações do plano...</p>
                      </div>
                      <PlanoBadge
                        v-else-if="!isLoading.clinica && clinica && clinica.plano && clinica.plano.id"
                        :plano="clinica.plano"
                        :assinatura="assinaturaAtiva || null"
                        size="medium"
                        variant="detailed"
                        :show-details="true"
                      />
                      <div v-else class="no-plan-message">
                        <div class="text-center py-3">
                          <i class="fas fa-exclamation-triangle text-warning mb-2" style="font-size: 2rem;"></i>
                          <p class="text-muted mb-2">Nenhum plano ativo</p>
                          <button
                            class="btn btn-primary btn-sm"
                            @click="abrirModalSeletorPlano"
                          >
                            <i class="fas fa-plus me-1"></i>
                            Definir Plano
                          </button>
                        </div>
                      </div>
                    </div>

                    <!-- Estatísticas -->
                    <div class="p-horizontal-divider mb-0 w-100"></div>
                    <div class="section-header">
                      <div class="section-header-content">
                        <div class="section-icon">
                          <v-icon>mdi-chart-bar</v-icon>
                        </div>
                        <p class="text-uppercase mb-0">Estatísticas</p>
                      </div>
                    </div>

                    <div class="clinic-info-block">
                      <div class="row">
                        <!-- Primeira coluna -->
                        <div class="col-md-4">
                          <div class="info-row">
                            <span class="info-label">Pacientes:</span>
                            <span class="info-value">{{ clinica.pacientes_count || 0 }}</span>
                          </div>
                          <div class="info-row">
                            <span class="info-label">Ortodontistas:</span>
                            <span class="info-value">{{ clinica.dentistas_count || 0 }}</span>
                          </div>
                        </div>

                        <!-- Segunda coluna -->
                        <div class="col-md-4">
                          <div class="info-row">
                            <span class="info-label">Usuários:</span>
                            <span class="info-value">{{ clinica.usuarios_count || 0 }}</span>
                          </div>
                          <div class="info-row">
                            <span class="info-label">Mentorias:</span>
                            <span class="info-value">{{ clinica.mentorias_count || 0 }}</span>
                          </div>
                        </div>

                        <!-- Terceira coluna -->
                        <div class="col-md-4">
                          <div class="info-row">
                            <span class="info-label">Imagens:</span>
                            <span class="info-value">{{ clinica.imagens_count || 0 }}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Meios de contato -->
                    <div class="p-horizontal-divider mb-0 w-100"></div>
                    <div class="section-header">
                      <div class="section-header-content">
                        <div class="section-icon">
                          <v-icon>mdi-phone</v-icon>
                        </div>
                        <p class="text-uppercase mb-0">Meios de contato</p>
                      </div>
                      <div class="section-actions">
                        <span
                          v-if="!isEditing.meiosContatos && clinica?.contatos?.length > 0"
                          class="edit-icon-wrapper-light"
                          title="Gerenciar meios de contato"
                          @click="toggleEditMode('meiosContatos')">
                          <v-icon class="edit-icon-light">mdi-pencil</v-icon>
                        </span>
                        <button
                          v-if="isEditing.meiosContatos"
                          class="btn-cancel-edit"
                          @click="cancelEditContatos"
                        >
                          <v-icon>mdi-close</v-icon>
                          Cancelar edição
                        </button>
                      </div>
                    </div>

                    <ClinicaContactInfo
                      :clinica="clinica"
                      :isEditing="isEditing.meiosContatos"
                      :isMobile="false"
                      :novoContato="novoContato"
                      :novoContatoMask="novoContatoMask"
                      :getContatoPlaceholder="getContatoPlaceholder"
                      @select-meio-contato="selectMeioContato"
                      @contato-change="contatoChange"
                      @adicionar-contato="adicionarContato"
                      @excluir-contato="excluirContato"
                      @update:field="updateClinicaField"
                    />
                  </div>
                </div>

                <!-- Listagem de Ortodontistas da Clínica -->
                <div class="row mt-4">
                  <div class="col-12">
                    <div class="card">
                      <div class="card-header pb-0">
                        <div class="d-flex align-items-center justify-content-between">
                          <h6 class="mb-0">Ortodontistas da Clínica</h6>
                          <div class="d-flex align-items-center">
                            <div class="input-group input-group-outline me-3" style="max-width: 300px;">
                              <input
                                type="text"
                                class="form-control"
                                placeholder="Buscar ortodontista..."
                                v-model="searchOrtodontista"
                                @input="searchOrtodontistas"
                              >
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="card-body px-0 pt-0 pb-2">
                        <div v-if="isLoading.ortodontistas" class="text-center py-4">
                          <div class="spinner-border text-primary" role="status"></div>
                        </div>
                        <div v-else-if="ortodontistasFiltrados.length === 0" class="text-center py-4">
                          <p class="text-muted">{{ searchOrtodontista ? 'Nenhum ortodontista encontrado com esse termo.' : 'Nenhum ortodontista encontrado para esta clínica.' }}</p>
                        </div>
                        <div v-else class="table-responsive p-0">
                          <table class="table align-items-center mb-0">
                            <thead>
                              <tr>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Ortodontista</th>
                                <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Contato</th>
                                <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Cadastrado</th>
                                <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Ações</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr v-for="ortodontista in ortodontistasFiltrados" :key="ortodontista.id">
                                <td>
                                  <div class="d-flex px-2 py-1">
                                    <div>
                                      <img
                                        v-if="ortodontista.profile_picture_url"
                                        :src="ortodontista.profile_picture_url"
                                        class="avatar avatar-sm me-3 border-radius-lg"
                                        alt="user_image"
                                      >
                                      <div v-else class="avatar avatar-sm me-3 border-radius-lg d-flex align-items-center justify-content-center bg-gradient-secondary">
                                        <v-icon class="text-white text-sm">mdi-doctor</v-icon>
                                      </div>
                                    </div>
                                    <div class="d-flex flex-column justify-content-center">
                                      <h6 class="mb-0 text-sm">{{ ortodontista.nome }}</h6>
                                      <p class="text-xs text-secondary mb-0">{{ ortodontista.email }}</p>
                                    </div>
                                  </div>
                                </td>
                                <td>
                                  <p class="text-xs font-weight-bold mb-0">{{ ortodontista.telefone || 'Não informado' }}</p>
                                  <p class="text-xs text-secondary mb-0">{{ ortodontista.endereco_cidade || '' }}</p>
                                </td>
                                <td class="align-middle text-center text-sm">
                                  <div class="d-flex justify-content-center">
                                    <span :class="ortodontista.ativo ? 'badge badge-sm bg-gradient-success' : 'badge badge-sm bg-gradient-secondary'">
                                      {{ ortodontista.ativo ? 'Ativo' : 'Inativo' }}
                                    </span>
                                  </div>
                                </td>
                                <td class="align-middle text-center">
                                  <span class="text-secondary text-xs font-weight-bold">{{ $filters.dateTime(ortodontista.created_at) }}</span>
                                </td>
                                <td class="align-middle text-center">
                                  <button
                                    class="btn btn-sm btn-outline-primary me-1"
                                    @click="openAlterarSenhaModal(ortodontista)"
                                    title="Alterar senha"
                                  >
                                    <i class="fas fa-key"></i>
                                  </button>
                                  <button
                                    class="btn btn-sm btn-outline-secondary"
                                    @click="viewOrtodontista(ortodontista)"
                                    title="Ver detalhes"
                                  >
                                    <i class="fas fa-eye"></i>
                                  </button>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Abas de Pacientes e Usuários removidas conforme solicitado -->
            </div>

            <!-- Conteúdo da aba Faturas -->
            <div v-else-if="activeTab === 'faturas'" class="clinic-content">
              <FaturasClinica
                v-if="clinica.id"
                :clinica="clinica"
                @fatura-alterada="handleFaturaAlterada"
              />
            </div>

            <!-- Conteúdo da aba Assinaturas -->
            <div v-else-if="activeTab === 'assinaturas'" class="clinic-content">
              <AssinaturasClinica
                v-if="clinica.id"
                :clinica="clinica"
                @assinatura-alterada="handleAssinaturaAlterada"
              />
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- Modal de Seleção de Plano -->
    <PlanoSelectorModal
      v-if="clinica && clinica.id"
      modal-id="planoSelectorModal"
      :clinica="clinica"
      :plano-atual="clinica.plano || null"
      :assinatura-atual="assinaturaAtiva || null"
      @plano-alterado="handlePlanoAlterado"
    />

    <!-- Modal de Alterar Senha -->
    <div class="modal fade" id="modalAlterarSenha" tabindex="-1" ref="modalAlterarSenha">
      <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">
              <i class="fas fa-key me-2"></i>
              Alterar Senha - {{ ortodontistaSelecionado?.nome }}
            </h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label class="form-label">Nova senha:</label>
              <div class="input-group">
                <input
                  type="password"
                  class="form-control"
                  v-model="novaSenha"
                  placeholder="Digite a nova senha"
                  :class="{ 'is-invalid': erroNovaSenha }"
                />
                <button
                  class="btn btn-outline-secondary"
                  type="button"
                  @click="gerarSenhaAleatoria"
                  title="Gerar senha aleatória"
                >
                  <i class="fas fa-random"></i>
                </button>
              </div>
              <div v-if="erroNovaSenha" class="invalid-feedback d-block">
                {{ erroNovaSenha }}
              </div>
            </div>
            <div class="mb-3">
              <label class="form-label">Confirmar senha:</label>
              <input
                type="password"
                class="form-control"
                v-model="confirmarSenha"
                placeholder="Confirme a nova senha"
                :class="{ 'is-invalid': erroConfirmarSenha }"
              />
              <div v-if="erroConfirmarSenha" class="invalid-feedback d-block">
                {{ erroConfirmarSenha }}
              </div>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              Cancelar
            </button>
            <button
              type="button"
              class="btn btn-primary"
              @click="alterarSenha"
              :disabled="isAlterandoSenha || !novaSenha || !confirmarSenha"
            >
              <span v-if="isAlterandoSenha" class="spinner-border spinner-border-sm me-2"></span>
              {{ isAlterandoSenha ? 'Alterando...' : 'Alterar Senha' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MaterialInput from "@/components/MaterialInput.vue";
import ClinicaContactInfo from "@/components/ClinicaContactInfo.vue";
import ClinicaAddressInfo from "@/components/ClinicaAddressInfo.vue";
import PlanoBadge from "@/components/PlanoBadge.vue";
import PlanoSelectorModal from "@/components/PlanoSelectorModal.vue";
import AssinaturasClinica from "@/components/AssinaturasClinica.vue";
import FaturasClinica from "@/components/FaturasClinica.vue";
import { getClinica, updateClinicaField } from "@/services/clinicasService";
import { getClinicaPacientes } from "@/services/pacientesService";
import { getClinicaDentistas } from "@/services/dentistasService";
import { assinaturasService } from "@/services/assinaturasService";
import { phoneMask } from "@/helpers/utils.js";
import cSwal from "@/utils/cSwal.js";
import { openModal, closeModal } from "@/utils/modalHelper";

export default {
  name: "Clinica",
  components: {
    MaterialInput,
    ClinicaContactInfo,
    ClinicaAddressInfo,
    PlanoBadge,
    PlanoSelectorModal,
    AssinaturasClinica,
    FaturasClinica,
  },
  data() {
    return {
      clinica: {
        nome: "",
        endereco: "",
        observacoes: "",
        pacientes_count: 0,
        usuarios_count: 0,
        mentorias_count: 0,
        imagens_count: 0,
        contatos: [],
        endereco_cep: "",
        endereco_estado: "",
        endereco_cidade: "",
        endereco_bairro: "",
        endereco_logradouro: "",
        endereco_numero: "",
        endereco_complemento: "",
      },
      originalClinica: null,
      pacientes: [],
      usuarios: [],
      ortodontistas: [],
      ortodontistasFiltrados: [],
      searchOrtodontista: '',
      activeTab: 'detalhes',
      pacientesLoaded: false,
      usuariosLoaded: false,
      assinaturaAtiva: null,
      isEditingEndereco: false,
      hoveredField: null,
      // Estados de edição individual
      isEditingNome: false,
      isEditingSlug: false,
      isEditingObservacoes: false,
      // Valores temporários para edição
      tempNome: '',
      tempSlug: '',
      tempObservacoes: '',
      isEditing: {
        meiosContatos: false,
      },
      novoContato: {
        tipo: "whatsapp",
        contato: "",
        descricao: "",
      },
      isLoading: {
        clinica: true,
        pacientes: false,
        usuarios: false,
        ortodontistas: false,
      },
      // Modal alterar senha
      ortodontistaSelecionado: null,
      novaSenha: '',
      confirmarSenha: '',
      erroNovaSenha: '',
      erroConfirmarSenha: '',
      isAlterandoSenha: false,
    };
  },
  async created() {
    await this.loadClinica();
  },

  watch: {
    clinica: {
      handler() {
        // Se o endereço estiver preenchido, não deve estar em modo de edição
        if (this.isFilledEndereco) {
          this.isEditingEndereco = false;
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    async loadClinica() {
      this.isLoading.clinica = true;
      try {
        const clinicaData = await getClinica(this.$route.params.id);
        if (clinicaData) {
          this.clinica = clinicaData;
          // Salvar cópia original para comparações
          this.originalClinica = JSON.parse(JSON.stringify(clinicaData));
          // Carregar ortodontistas após carregar a clínica
          await this.loadOrtodontistas();
          // Carregar assinatura ativa
          await this.loadAssinaturaAtiva();
        }
      } catch (error) {
        console.error('Erro ao carregar clínica:', error);
      }
      this.isLoading.clinica = false;
    },

    async loadAssinaturaAtiva() {
      try {
        this.assinaturaAtiva = await assinaturasService.getAssinaturaAtiva(this.clinica.id);
      } catch (error) {
        console.error('Erro ao carregar assinatura ativa:', error);
        this.assinaturaAtiva = null;
      }
    },

    async loadPacientes() {
      if (this.pacientesLoaded) return; // Já carregados

      this.isLoading.pacientes = true;
      try {
        const pacientesData = await getClinicaPacientes(this.clinica.slug);
        if (pacientesData && Array.isArray(pacientesData)) {
          this.pacientes = pacientesData;
          this.pacientesLoaded = true;
        }
      } catch (error) {
        console.error('Erro ao carregar pacientes:', error);
      }
      this.isLoading.pacientes = false;
    },

    async loadUsuarios() {
      if (this.usuariosLoaded) return; // Já carregados

      this.isLoading.usuarios = true;
      try {
        const usuariosData = await getClinicaDentistas(this.clinica.slug);
        if (usuariosData) {
          this.usuarios = usuariosData;
          this.usuariosLoaded = true;
        }
      } catch (error) {
        console.error('Erro ao carregar usuários:', error);
      }
      this.isLoading.usuarios = false;
    },

    async loadOrtodontistas() {
      this.isLoading.ortodontistas = true;
      try {
        const ortodontistasData = await getClinicaDentistas(this.clinica.slug);
        if (ortodontistasData) {
          this.ortodontistas = ortodontistasData;
          this.ortodontistasFiltrados = ortodontistasData;
        }
      } catch (error) {
        console.error('Erro ao carregar ortodontistas:', error);
      }
      this.isLoading.ortodontistas = false;
    },

    searchOrtodontistas() {
      if (!this.searchOrtodontista.trim()) {
        this.ortodontistasFiltrados = this.ortodontistas;
        return;
      }

      const searchTerm = this.searchOrtodontista.toLowerCase();
      this.ortodontistasFiltrados = this.ortodontistas.filter(ortodontista =>
        ortodontista.nome.toLowerCase().includes(searchTerm) ||
        (ortodontista.email && ortodontista.email.toLowerCase().includes(searchTerm)) ||
        (ortodontista.telefone && ortodontista.telefone.includes(searchTerm))
      );
    },

    openTab(tab) {
      this.activeTab = tab;

      // Carregar dados conforme a aba selecionada
      if (tab === 'pacientes') {
        this.loadPacientes();
      } else if (tab === 'usuarios') {
        this.loadUsuarios();
      }
    },

    // Métodos para edição individual dos campos
    showEditIcon(field) {
      this.hoveredField = field;
    },

    hideEditIcon(field) {
      if (this.hoveredField === field) {
        this.hoveredField = null;
      }
    },

    editField(field) {
      if (field === 'nome') {
        this.tempNome = this.clinica.nome || '';
        this.isEditingNome = true;
      } else if (field === 'slug') {
        this.tempSlug = this.clinica.slug || '';
        this.isEditingSlug = true;
      } else if (field === 'observacoes') {
        this.tempObservacoes = this.clinica.observacoes || '';
        this.isEditingObservacoes = true;
      }
    },

    async saveField(field) {
      // Mostrar confirmação antes de salvar
      cSwal.cConfirm(
        `Deseja realmente salvar as alterações no campo ${this.getFieldLabel(field)}?`,
        async () => {
          try {
            // Mostrar loading
            cSwal.loading('Salvando...');

            const updateData = {};

            if (field === 'nome') {
              updateData.nome = this.tempNome;
              this.clinica.nome = this.tempNome;
              this.isEditingNome = false;
            } else if (field === 'slug') {
              updateData.slug = this.tempSlug;
              this.clinica.slug = this.tempSlug;
              this.isEditingSlug = false;
            } else if (field === 'observacoes') {
              updateData.observacoes = this.tempObservacoes;
              this.clinica.observacoes = this.tempObservacoes;
              this.isEditingObservacoes = false;
            }

            // Fazer requisição PATCH para a API usando o serviço
            const fieldValue = updateData[field];
            const result = await updateClinicaField(this.clinica.id, field, fieldValue);

            // Fechar loading
            cSwal.loaded();

            if (!result) {
              throw new Error('Erro ao atualizar clínica');
            }

            // Mostrar sucesso
            cSwal.cSuccess(`Campo ${this.getFieldLabel(field)} atualizado com sucesso!`);

            console.log(`Campo ${field} atualizado com sucesso`);
          } catch (error) {
            // Fechar loading
            cSwal.loaded();

            console.error('Erro ao salvar campo:', error);

            // Mostrar erro
            cSwal.cError(`Erro ao salvar ${this.getFieldLabel(field)}. Tente novamente.`);

            // Reverter mudanças em caso de erro
            if (field === 'nome') {
              this.tempNome = this.clinica.nome;
            } else if (field === 'slug') {
              this.tempSlug = this.clinica.slug;
            } else if (field === 'observacoes') {
              this.tempObservacoes = this.clinica.observacoes;
            }
          }
        }
      );
    },

    getFieldLabel(field) {
      const labels = {
        'nome': 'Nome',
        'slug': 'Slug',
        'observacoes': 'Observações',
        'endereco_cep': 'CEP',
        'endereco_estado': 'Estado',
        'endereco_cidade': 'Cidade',
        'endereco_logradouro': 'Logradouro',
        'endereco_numero': 'Número',
        'endereco_bairro': 'Bairro',
        'endereco_complemento': 'Complemento'
      };
      return labels[field] || field;
    },

    cancelEditField(field) {
      if (field === 'nome') {
        this.isEditingNome = false;
        this.tempNome = '';
      } else if (field === 'slug') {
        this.isEditingSlug = false;
        this.tempSlug = '';
      } else if (field === 'observacoes') {
        this.isEditingObservacoes = false;
        this.tempObservacoes = '';
      }
    },

    // Métodos para endereço
    toggleEditEndereco() {
      this.isEditingEndereco = !this.isEditingEndereco;
    },

    cancelEditEndereco() {
      // Verificar se há alterações nos campos de endereço
      const addressFields = ['endereco_cep', 'endereco_estado', 'endereco_cidade', 'endereco_logradouro', 'endereco_numero', 'endereco_bairro', 'endereco_complemento'];
      const hasChanges = addressFields.some(field => {
        // Comparar com dados originais se existirem
        return this.originalClinica && this.clinica[field] !== this.originalClinica[field];
      });

      if (!hasChanges) {
        // Sem alterações, cancelar diretamente
        this.isEditingEndereco = false;
        return;
      }

      // Com alterações, pedir confirmação
      cSwal.cConfirm(
        "Deseja realmente cancelar a edição? Todas as alterações não salvas serão perdidas.",
        () => {
          // Restaurar valores originais dos campos de endereço se existirem
          if (this.originalClinica) {
            addressFields.forEach(field => {
              if (this.originalClinica[field] !== undefined) {
                this.clinica[field] = this.originalClinica[field];
              }
            });
          }

          // Desativar modo de edição
          this.isEditingEndereco = false;
        }
      );
    },

    async saveEditEndereco() {
      // Verificar se há alterações nos campos de endereço
      const addressFields = ['endereco_cep', 'endereco_estado', 'endereco_cidade', 'endereco_logradouro', 'endereco_numero', 'endereco_bairro', 'endereco_complemento'];
      const hasChanges = addressFields.some(field => {
        return this.originalClinica && this.clinica[field] !== this.originalClinica[field];
      });

      if (!hasChanges) {
        this.isEditingEndereco = false;
        return;
      }

      // Confirmar salvamento
      cSwal.cConfirm(
        "Deseja realmente salvar as alterações no endereço?",
        async () => {
          try {
            cSwal.loading('Salvando...');

            // Salvar cada campo alterado
            for (const field of addressFields) {
              if (this.originalClinica[field] !== this.clinica[field]) {
                const result = await updateClinicaField(this.clinica.id, field, this.clinica[field]);
                if (!result) {
                  throw new Error(`Erro ao atualizar ${field}`);
                }
              }
            }

            // Atualizar dados originais
            addressFields.forEach(field => {
              this.originalClinica[field] = this.clinica[field];
            });

            cSwal.loaded();
            cSwal.cSuccess('Endereço atualizado com sucesso!');
            this.isEditingEndereco = false;

          } catch (error) {
            cSwal.loaded();
            console.error('Erro ao salvar endereço:', error);
            cSwal.cError('Erro ao salvar endereço. Tente novamente.');
          }
        }
      );
    },



    validarCep(cep) {
      if (!cep) return false;
      return /^\d{8}$/.test(cep.replace(/[^\d]+/g, ""));
    },

    async getEndereco(event) {
      clearTimeout(this.timeout);
      this.timeout = setTimeout(async () => {
        var cep = event.target.value;
        cep = this.clinica.endereco_cep;

        if (!this.validarCep(cep)) return false;

        const { getEnderecoByCep } = await import("@/services/commonService");
        const enderecoInfo = await getEnderecoByCep(cep);
        if (!enderecoInfo) return false;

        this.clinica.endereco_logradouro = enderecoInfo.street;
        this.clinica.endereco_cidade = enderecoInfo.city;
        this.clinica.endereco_estado = enderecoInfo.state;
        this.clinica.endereco_bairro = enderecoInfo.district;

        if (!this.clinica.endereco_numero) {
          this.$nextTick(() => {
            const numeroInput = document.getElementById('clinica_enderecoNumero');
            if (numeroInput) {
              numeroInput.focus();
            }
          });
        }
      }, 50);
    },

    async updateClinicaField(update) {
      // Verificar se é um campo de endereço
      const addressFields = ['endereco_cep', 'endereco_estado', 'endereco_cidade', 'endereco_logradouro', 'endereco_numero', 'endereco_bairro', 'endereco_complemento'];
      
      // Se for campo de endereço e estiver em modo de edição, apenas atualizar localmente
      if (addressFields.includes(update.field) && this.isEditingEndereco) {
        this.clinica[update.field] = update.value;
        return;
      }

      // Para outros campos, salvar imediatamente com confirmação
      cSwal.cConfirm(
        `Deseja realmente salvar as alterações no campo ${this.getFieldLabel(update.field)}?`,
        async () => {
          try {
            // Mostrar loading
            cSwal.loading('Salvando...');

            // Atualizar o campo localmente
            this.clinica[update.field] = update.value;

            // Fazer requisição PATCH para a API usando o serviço
            const result = await updateClinicaField(this.clinica.id, update.field, update.value);

            // Fechar loading
            cSwal.loaded();

            if (!result) {
              throw new Error('Erro ao atualizar campo da clínica');
            }

            // Mostrar sucesso
            cSwal.cSuccess(`Campo ${this.getFieldLabel(update.field)} atualizado com sucesso!`);

            console.log(`Campo ${update.field} atualizado com sucesso`);
          } catch (error) {
            // Fechar loading
            cSwal.loaded();

            console.error('Erro ao atualizar campo da clínica:', error);

            // Mostrar erro
            cSwal.cError(`Erro ao salvar ${this.getFieldLabel(update.field)}. Tente novamente.`);

            // TODO: Reverter mudança em caso de erro
          }
        }
      );
    },

    // Métodos para gerenciamento de contatos
    phoneMask,

    toggleEditMode(mode) {
      this.isEditing[mode] = !this.isEditing[mode];
    },

    cancelEditContatos() {
      this.isEditing.meiosContatos = false;
      // Reset novo contato
      this.novoContato = {
        tipo: "whatsapp",
        contato: "",
        descricao: "",
      };
    },

    selectMeioContato(tipo) {
      this.novoContato.tipo = tipo;
    },

    contatoChange(event) {
      // Implementar se necessário
    },

    adicionarContato() {
      // Implementar adição de contato para clínica
      console.log('Adicionar contato:', this.novoContato);
    },

    excluirContato(id, tipo) {
      // Implementar exclusão de contato para clínica
      console.log('Excluir contato:', id, tipo);
    },

    handleAssinaturaAlterada(data) {
      // Atualizar dados da clínica quando assinatura for alterada
      console.log('Assinatura alterada:', data);
      this.loadClinica(); // Recarregar dados da clínica
    },

    handleFaturaAlterada(data) {
      // Atualizar dados quando fatura for alterada
      console.log('Fatura alterada:', data);
      // Pode implementar lógica específica se necessário
    },

    handlePlanoAlterado(data) {
      // Atualizar dados da clínica quando plano for alterado
      console.log('Plano alterado:', data);
      this.clinica.plano = data.novoPlano;
      this.assinaturaAtiva = data.assinatura;
      this.loadClinica(); // Recarregar dados completos da clínica
    },

    abrirModalSeletorPlano() {
      // Verificar se a clínica está carregada antes de abrir o modal
      if (!this.clinica || !this.clinica.id) {
        console.warn('Clínica não carregada ainda');
        return;
      }

      // Aguardar um pouco para garantir que o modal foi renderizado
      this.$nextTick(() => {
        openModal('planoSelectorModal');
      });
    },

    // Métodos para alterar senha
    openAlterarSenhaModal(ortodontista) {
      this.ortodontistaSelecionado = ortodontista;
      this.novaSenha = '';
      this.confirmarSenha = '';
      this.erroNovaSenha = '';
      this.erroConfirmarSenha = '';

      // Abrir modal usando modalHelper
      openModal('modalAlterarSenha');
    },

    gerarSenhaAleatoria() {
      const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%&*";
      let password = "";
      for (let i = 0; i < 12; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
      }
      this.novaSenha = password;
      this.confirmarSenha = password;
    },

    async alterarSenha() {
      // Limpar erros
      this.erroNovaSenha = '';
      this.erroConfirmarSenha = '';

      // Validações
      if (!this.novaSenha) {
        this.erroNovaSenha = 'Nova senha é obrigatória';
        return;
      }

      if (this.novaSenha.length < 6) {
        this.erroNovaSenha = 'A senha deve ter pelo menos 6 caracteres';
        return;
      }

      if (this.novaSenha !== this.confirmarSenha) {
        this.erroConfirmarSenha = 'As senhas não coincidem';
        return;
      }

      this.isAlterandoSenha = true;

      try {
        // Aqui você implementaria a chamada para o backend
        // Por enquanto, vou simular uma chamada
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Fechar modal
        closeModal('modalAlterarSenha');

        // Mostrar sucesso
        cSwal.fire({
          icon: 'success',
          title: 'Sucesso!',
          text: 'Senha alterada com sucesso!',
          timer: 2000,
          showConfirmButton: false
        });

      } catch (error) {
        console.error('Erro ao alterar senha:', error);
        cSwal.fire({
          icon: 'error',
          title: 'Erro!',
          text: 'Erro ao alterar senha. Tente novamente.',
        });
      } finally {
        this.isAlterandoSenha = false;
      }
    },

    viewOrtodontista(ortodontista) {
      // Implementar visualização de detalhes do ortodontista
      console.log('Ver ortodontista:', ortodontista);
      // Você pode implementar um modal ou navegar para uma página específica
    },

  },

  computed: {
    novoContatoMask() {
      return this.phoneMask(this.novoContato.tipo);
    },

    getContatoPlaceholder() {
      switch (this.novoContato.tipo) {
        case 'email':
          return '<EMAIL>';
        case 'telefone':
          return '(11) 1234-5678';
        case 'celular':
          return '(11) 91234-5678';
        case 'whatsapp':
          return '(11) 91234-5678';
        default:
          return 'Digite o contato';
      }
    },

    isFilledEndereco() {
      return this.clinica.endereco_cep ||
             this.clinica.endereco_estado ||
             this.clinica.endereco_cidade ||
             this.clinica.endereco_logradouro;
    },
  },
};
</script>

<style scoped>
.clinic-name {
  color: #344767;
  font-weight: 600;
}

.clinic-info {
  min-width: 0;
  max-width: calc(100% - 40px);
}

.nav-tab {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-tab:hover {
  background-color: rgba(90, 155, 213, 0.1);
}

.nav-tab.active {
  background-color: rgba(90, 155, 213, 0.15) !important;
  color: #5a9bd5 !important;
  font-weight: 600;
}

.card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: none;
}

.profile-pic {
  width: 74px;
  height: 74px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Navbar horizontal em todas as telas */
.menu-2x2 {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  justify-content: space-between !important;
  width: 100%;
}

.menu-2x2 .nav-item {
  flex: 1;
  text-align: center;
}

/* Ajustes para telas ≤ 1024px - NAVBAR COMPACTA */
@media (max-width: 1024px) {
  .menu-2x2 {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 0.2rem !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }

  .menu-2x2 .nav-item {
    margin: 0 2px;
  }

  .menu-2x2 .nav-item .nav-link {
    padding: 0.4rem 0.2rem !important;
    font-size: 0.8rem;
    min-height: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    background-color: white;
  }

  .menu-2x2 .nav-item .nav-link.active {
    transform: translateY(-1px);
    box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
  }

  .menu-2x2 .nav-item .nav-link i {
    font-size: 1rem;
    margin-bottom: 0.2rem;
  }

  .menu-2x2 .nav-item .nav-link span {
    font-size: 0.65rem;
    font-weight: 500;
    line-height: 1;
  }
}

/* Estilos baseados no Paciente.vue */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 6px;
  margin: 15px 0 10px;
  border-radius: 8px;
  background: linear-gradient(to right, #f8f9fa, #f1f3f5);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border-left: 4px solid #5a9bd5;
  transition: all 0.2s ease;
  flex-wrap: nowrap;
  flex-direction: row;
}

.section-header:hover {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.section-header-content {
  display: flex;
  align-items: center;
  max-width: calc(100% - 80px);
  overflow: hidden;
  flex-shrink: 1;
  flex-direction: row;
  flex-wrap: wrap;
}

.section-icon {
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-right: 0;
  background-color: rgba(90, 155, 213, 0.1);
  color: #5a9bd5;
  flex-shrink: 0;
}

.section-icon svg {
  font-size: 14pt;
}

.section-header p {
  font-weight: 600;
  font-size: 0.8rem;
  color: #495057;
  letter-spacing: 0.3px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-left: 8px;
  display: inline-flex;
  align-items: center;
}

.section-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.edit-icon-wrapper-light {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(90, 155, 213, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-icon-wrapper-light:hover {
  background-color: rgba(90, 155, 213, 0.2);
  transform: scale(1.1);
}

.edit-icon-light {
  color: #5a9bd5;
  font-size: 12px;
}

.btn-cancel-edit {
  background: none;
  border: 1px solid #dc3545;
  color: #dc3545;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.btn-cancel-edit:hover {
  background-color: #dc3545;
  color: white;
}

.clinic-info-block {
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f3f5;
  gap: 8px;
}

.info-row:last-child {
  border-bottom: none;
}

.info-row.editable-row {
  transition: background-color 0.2s ease;
  border-radius: 4px;
  margin: 0 -8px;
  padding: 8px;
}

.info-row.editable-row:hover {
  background-color: rgba(90, 155, 213, 0.05);
}

.info-label {
  font-weight: 600;
  color: #495057;
  font-size: 0.85rem;
  flex-shrink: 0;
  margin-right: 16px;
  min-width: 120px;
}

.info-value-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
}

.info-value {
  color: #6c757d;
  font-size: 0.85rem;
  font-weight: 600;
  word-break: break-word;
}

.edit-icon-small {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(90, 155, 213, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 8px;
  opacity: 0;
  transform: scale(0.8);
}

.editable-row:hover .edit-icon-small {
  opacity: 1;
  transform: scale(1);
}

/* Estilos para edição inline */
.edit-field-container {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  width: 100%;
}

.edit-field-input {
  flex: 1;
  min-width: 0;
}

.edit-field-actions {
  display: flex;
  gap: 0.25rem;
  align-items: flex-start;
  margin-top: 0.5rem;
}

.edit-field-actions .btn {
  padding: 0.25rem 0.5rem;
  min-width: auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-icon-small:hover {
  background-color: rgba(90, 155, 213, 0.2);
  transform: scale(1.1);
}

.edit-icon-small .v-icon {
  color: #5a9bd5;
  font-size: 12px;
}

.p-horizontal-divider {
  height: 1px;
  background: linear-gradient(to right, transparent, #dee2e6, transparent);
  margin: 20px 0;
}

.border-end-md {
  border-right: 1px solid #dee2e6;
}

@media (max-width: 768px) {
  .border-end-md {
    border-right: none;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 20px;
    margin-bottom: 20px;
  }

  .col-12.col-md-6.px-4 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }
}
</style>
