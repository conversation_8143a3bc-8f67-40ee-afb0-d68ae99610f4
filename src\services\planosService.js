import axios from '@/services/axios';

export const planosService = {
  /**
   * Obter todos os planos com filtros
   */
  async getPlanos(filters = {}) {
    const params = new URLSearchParams();

    if (filters.ativo !== undefined) params.append('ativo', filters.ativo);
    if (filters.modulo_ortodontia !== undefined) params.append('modulo_ortodontia', filters.modulo_ortodontia);
    if (filters.order_by) params.append('order_by', filters.order_by);
    if (filters.order_direction) params.append('order_direction', filters.order_direction);

    try {
      const response = await axios.get(`/planos?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar planos:', error);
      throw error;
    }
  },

  /**
   * Obter plano específico
   */
  async getPlano(id) {
    try {
      const response = await axios.get(`/planos/${id}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar plano:', error);
      throw error;
    }
  },

  /**
   * Criar novo plano
   */
  async createPlano(data) {
    try {
      const response = await axios.post('/planos', data);
      return response.data;
    } catch (error) {
      console.error('Erro ao criar plano:', error);
      throw error;
    }
  },

  /**
   * Atualizar plano
   */
  async updatePlano(id, data) {
    try {
      const response = await axios.put(`/planos/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Erro ao atualizar plano:', error);
      throw error;
    }
  },

  /**
   * Excluir plano
   */
  async deletePlano(id) {
    try {
      const response = await axios.delete(`/planos/${id}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao excluir plano:', error);
      throw error;
    }
  },

  /**
   * Alternar status do plano (ativo/inativo)
   */
  async toggleStatus(id) {
    try {
      const response = await axios.patch(`/planos/${id}/toggle-status`);
      return response.data;
    } catch (error) {
      console.error('Erro ao alterar status do plano:', error);
      throw error;
    }
  },

  /**
   * Obter planos disponíveis para clínicas
   */
  async getPlanosForClinicas() {
    try {
      const response = await axios.get('/planos-for-clinicas');
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar planos para clínicas:', error);
      throw error;
    }
  },

  /**
   * Validar dados do plano
   */
  validatePlanoData(data) {
    const errors = {};

    if (!data.nome || data.nome.trim() === '') {
      errors.nome = 'Nome é obrigatório';
    }

    if (data.quantidade_usuarios !== null && data.quantidade_usuarios < 1) {
      errors.quantidade_usuarios = 'Quantidade de usuários deve ser maior que 0';
    }

    if (data.quantidade_agendas_cadeiras !== null && data.quantidade_agendas_cadeiras < 1) {
      errors.quantidade_agendas_cadeiras = 'Quantidade de agendas/cadeiras deve ser maior que 0';
    }

    if (data.quantidade_agendas !== null && data.quantidade_agendas < 1) {
      errors.quantidade_agendas = 'Quantidade de agendas deve ser maior que 0';
    }

    if (data.quantidade_cadeiras !== null && data.quantidade_cadeiras < 1) {
      errors.quantidade_cadeiras = 'Quantidade de cadeiras deve ser maior que 0';
    }

    if (data.meses_fidelidade_minima < 0) {
      errors.meses_fidelidade_minima = 'Meses de fidelidade não pode ser negativo';
    }

    if (data.meses_gratuitos < 0) {
      errors.meses_gratuitos = 'Meses gratuitos não pode ser negativo';
    }

    if (data.quantidade_mentorias_mensais !== null && data.quantidade_mentorias_mensais < 1) {
      errors.quantidade_mentorias_mensais = 'Quantidade de mentorias deve ser maior que 0';
    }

    if (data.valor_mensal !== null && data.valor_mensal < 0) {
      errors.valor_mensal = 'Valor mensal não pode ser negativo';
    }

    // Validação específica: mentorias só podem ser definidas se módulo ortodontia estiver ativo
    if (data.quantidade_mentorias_mensais !== null && !data.modulo_ortodontia) {
      errors.quantidade_mentorias_mensais = 'Mentorias só podem ser definidas se o módulo de ortodontia estiver ativo';
    }

    // Validação: módulo clínica é obrigatório
    if (!data.modulo_clinica) {
      errors.modulo_clinica = 'O módulo clínica é obrigatório';
    }

    // Validação: cor deve ser hexadecimal válida
    if (data.cor && !/^#[0-9A-Fa-f]{6}$/.test(data.cor)) {
      errors.cor = 'Cor deve ser um código hexadecimal válido (ex: #007bff)';
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  },

  /**
   * Formatar valor monetário
   */
  formatCurrency(value) {
    if (value === null || value === undefined) {
      return 'Gratuito';
    }
    
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  },

  /**
   * Formatar quantidade (null = ilimitado)
   */
  formatQuantity(value) {
    return value === null ? 'Ilimitado' : value.toString();
  },

  /**
   * Obter cores padrão para planos
   */
  getDefaultColors() {
    return [
      '#007bff', // Azul
      '#28a745', // Verde
      '#ffc107', // Amarelo
      '#dc3545', // Vermelho
      '#6f42c1', // Roxo
      '#fd7e14', // Laranja
      '#20c997', // Teal
      '#6c757d', // Cinza
    ];
  },

  /**
   * Gerar dados padrão para novo plano
   */
  getDefaultPlanoData() {
    return {
      nome: '',
      observacoes: '',
      cor: '#007bff',
      ativo: true,
      modulo_clinica: true,
      modulo_ortodontia: false,
      quantidade_usuarios: null,
      quantidade_agendas_cadeiras: null,
      quantidade_agendas: null,
      quantidade_cadeiras: null,
      meses_fidelidade_minima: 0,
      meses_gratuitos: 0,
      quantidade_mentorias_mensais: null,
      valor_mensal: null,
    };
  },

  /**
   * Verificar se plano tem módulo ortodontia
   */
  hasOrtodontiaModule(plano) {
    return plano && plano.modulo_ortodontia === true;
  },

  /**
   * Verificar se plano é gratuito
   */
  isFree(plano) {
    return plano && (plano.valor_mensal === null || plano.valor_mensal === 0);
  },

  /**
   * Obter resumo do plano para exibição
   */
  getPlanoSummary(plano) {
    if (!plano) return null;

    return {
      nome: plano.nome,
      valor: this.formatCurrency(plano.valor_mensal),
      usuarios: this.formatQuantity(plano.quantidade_usuarios),
      ortodontistas: this.formatQuantity(plano.quantidade_ortodontistas),
      agendas: this.formatQuantity(plano.quantidade_agendas),
      cadeiras: this.formatQuantity(plano.quantidade_cadeiras),
      fidelidade: `${plano.meses_fidelidade_minima} meses`,
      mentorias: plano.modulo_ortodontia ? this.formatQuantity(plano.quantidade_mentorias_mensais) : 'N/A',
      moduloOrtodontia: plano.modulo_ortodontia,
      cor: plano.cor || '#007bff'
    };
  }
};

export default planosService;
