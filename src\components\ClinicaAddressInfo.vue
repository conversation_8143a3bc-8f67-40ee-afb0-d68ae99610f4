<template>
  <div>
    <div class="row mt-2">
      <!-- Primeira linha: CEP, Estado, Cidade -->
      <div class="col-sm-4 col-lg-3 mb-2">
        <MaterialInput
          label="CEP"
          type="text"
          v-model="localData.endereco_cep"
          :input="handleFieldInput('endereco_cep', true)"
          mask="#####-###"
          :id="'clinica_enderecoCep' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
        />
      </div>
      <div class="col-sm-4 col-lg-4 mb-2">
        <MaterialInput
          label="Estado"
          type="text"
          v-model="localData.endereco_estado"
          :id="'clinica_enderecoEstado' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
          :input="handleFieldInput('endereco_estado')"
        />
      </div>
      <div class="col-sm-4 col-lg-5 mb-2">
        <MaterialInput
          label="Cidade"
          type="text"
          v-model="localData.endereco_cidade"
          :id="'clinica_enderecoCidade' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
          :input="handleFieldInput('endereco_cidade')"
        />
      </div>

      <div class="col-sm-6 col-lg-8 mb-2">
        <MaterialInput
          label="Logradouro"
          type="text"
          v-model="localData.endereco_logradouro"
          :id="'clinica_enderecoLogradouro' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
          :input="handleFieldInput('endereco_logradouro')"
        />
      </div>
      <div class="col-sm-6 col-lg-4 mb-2">
        <MaterialInput
          label="Nº"
          type="text"
          v-model="localData.endereco_numero"
          :id="'clinica_enderecoNumero' + (isMobile ? '_mobile' : '')"
          :ref="isMobile ? null : 'endereco_numero'"
          :readonly="!isEditing"
          :input="handleFieldInput('endereco_numero')"
        />
      </div>

      <div class="col-sm-6 col-lg-5 mb-2">
        <MaterialInput
          label="Bairro"
          type="text"
          v-model="localData.endereco_bairro"
          :id="'clinica_enderecoBairro' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
          :input="handleFieldInput('endereco_bairro')"
        />
      </div>

      <div class="col-sm-6 col-lg-7">
        <MaterialInput
          label="Complemento"
          type="text"
          v-model="localData.endereco_complemento"
          :id="'clinica_enderecoComplemento' + (isMobile ? '_mobile' : '')"
          :readonly="!isEditing"
          :input="handleFieldInput('endereco_complemento')"
        />
      </div>
    </div>
  </div>
</template>

<script>
import MaterialInput from "@/components/MaterialInput.vue";

export default {
  name: "ClinicaAddressInfo",
  components: {
    MaterialInput
  },
  props: {
    clinica: {
      type: Object,
      required: true
    },
    isEditing: {
      type: Boolean,
      default: false
    },
    isMobile: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      localData: {
        endereco_cep: this.clinica.endereco_cep,
        endereco_estado: this.clinica.endereco_estado,
        endereco_cidade: this.clinica.endereco_cidade,
        endereco_bairro: this.clinica.endereco_bairro,
        endereco_logradouro: this.clinica.endereco_logradouro,
        endereco_numero: this.clinica.endereco_numero,
        endereco_complemento: this.clinica.endereco_complemento
      }
    };
  },
  watch: {
    clinica: {
      handler(newVal) {
        this.localData = {
          endereco_cep: newVal.endereco_cep,
          endereco_estado: newVal.endereco_estado,
          endereco_cidade: newVal.endereco_cidade,
          endereco_bairro: newVal.endereco_bairro,
          endereco_logradouro: newVal.endereco_logradouro,
          endereco_numero: newVal.endereco_numero,
          endereco_complemento: newVal.endereco_complemento
        };
      },
      deep: true
    }
  },
  methods: {
    updateField(field, value) {
      this.$emit('update:field', { field, value });
    },
    handleFieldInput(fieldName, isCep = false) {
      return (event) => {
        if (event && event.target) {
          this.localData[fieldName] = event.target.value;
          this.updateField(fieldName, this.localData[fieldName]);

          // Se for o campo CEP, emitir o evento get-endereco
          if (isCep) {
            this.$emit('get-endereco', event);
          }
        }
      };
    }
  }
};
</script>
