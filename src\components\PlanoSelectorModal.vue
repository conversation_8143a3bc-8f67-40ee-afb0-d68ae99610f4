<template>
  <div class="modal fade" :id="modalId" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header border-0 pb-0">
          <div class="modal-title-container">
            <h5 class="modal-title">
              <i class="fas fa-layer-group text-primary me-2"></i>
              Alterar Plano da Clínica
            </h5>
            <p class="modal-subtitle mb-0">
              Selecione o novo plano para <strong>{{ clinica?.nome }}</strong>
            </p>
          </div>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>

        <div class="modal-body">
          <!-- Loading -->
          <div v-if="isLoading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Carregando...</span>
            </div>
            <p class="mt-2 text-muted">Carregando planos disponíveis...</p>
          </div>

          <!-- Plano Atual -->
          <div v-if="planoAtual && !isLoading" class="current-plan-section mb-3">
            <h6 class="section-title">
              <i class="fas fa-star text-warning me-2"></i>
              Plano Atual
            </h6>
            <div class="current-plan-card">
              <div class="row align-items-center">
                <div class="col-md-8">
                  <PlanoBadge
                    v-if="planoAtual"
                    :plano="planoAtual"
                    :assinatura="assinaturaAtual"
                    size="small"
                    variant="compact"
                    :show-details="false"
                  />
                </div>
                <div class="col-md-4">
                  <div class="billing-info">
                    <div class="billing-title">
                      <i class="fas fa-calendar-alt me-1"></i>
                      Próxima Cobrança
                    </div>
                    <div class="billing-date">
                      {{ getNextBillingDate() }}
                    </div>
                    <div class="billing-amount">
                      {{ formatCurrency(planoAtual?.valor_mensal) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Lista de Planos Disponíveis -->
          <div v-if="!isLoading" class="available-plans-section">
            <h6 class="section-title">
              <i class="fas fa-list text-info me-2"></i>
              Planos Disponíveis
            </h6>
            
            <div class="plans-grid">
              <div 
                v-for="plano in planosDisponiveis" 
                :key="plano.id"
                class="plan-card"
                :class="{ 
                  'plan-card--selected': selectedPlano?.id === plano.id,
                  'plan-card--current': plano.id === planoAtual?.id
                }"
                @click="selectPlano(plano)"
              >
                <div class="plan-card-header" :style="{ backgroundColor: plano.cor }">
                  <div class="plan-icon">
                    <i :class="getPlanoIcon(plano)"></i>
                  </div>
                  <div class="plan-badge" v-if="plano.id === planoAtual?.id">
                    <span>Atual</span>
                  </div>
                </div>
                
                <div class="plan-card-body">
                  <div class="plan-header-info">
                    <h6 class="plan-name">{{ plano.nome }}</h6>
                    <div class="plan-price">
                      {{ formatCurrency(plano.valor_mensal) }}
                      <span class="price-period">/mês</span>
                    </div>
                  </div>

                  <div class="plan-features-grid">
                    <div class="features-column">
                      <div class="feature-item" v-if="plano.quantidade_usuarios">
                        <i class="fas fa-users"></i>
                        <span>{{ formatQuantity(plano.quantidade_usuarios) }} usuários</span>
                      </div>
                      <div class="feature-item" v-if="plano.quantidade_agendas">
                        <i class="fas fa-calendar"></i>
                        <span>{{ formatQuantity(plano.quantidade_agendas) }} agendas</span>
                      </div>
                    </div>
                    <div class="features-column">
                      <div class="feature-item" v-if="plano.modulo_ortodontia">
                        <i class="fas fa-tooth"></i>
                        <span>Ortodontia</span>
                      </div>
                      <div class="feature-item" v-if="plano.quantidade_mentorias_mensais">
                        <i class="fas fa-graduation-cap"></i>
                        <span>{{ formatQuantity(plano.quantidade_mentorias_mensais) }} mentorias</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="plan-card-footer">
                  <div class="selection-indicator">
                    <i class="fas fa-check-circle"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Configurações da Alteração -->
          <div v-if="selectedPlano && selectedPlano.id !== planoAtual?.id" class="change-settings mt-4">
            <h6 class="section-title">
              <i class="fas fa-cog text-secondary me-2"></i>
              Configurações da Alteração
            </h6>
            
            <div class="row g-3">
              <div class="col-md-6">
                <label class="form-label">Data de Início</label>
                <input 
                  type="date" 
                  class="form-control"
                  v-model="dataInicio"
                  :min="minDate"
                >
              </div>
              <div class="col-md-6">
                <label class="form-label">Motivo da Alteração (opcional)</label>
                <input 
                  type="text" 
                  class="form-control"
                  v-model="motivoAlteracao"
                  placeholder="Ex: Upgrade para mais funcionalidades"
                >
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer border-0 pt-0">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="fas fa-times me-2"></i>
            Cancelar
          </button>
          <button 
            type="button" 
            class="btn btn-primary"
            :disabled="!canConfirm"
            @click="confirmarAlteracao"
          >
            <i class="fas fa-check me-2"></i>
            Confirmar Alteração
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PlanoBadge from '@/components/PlanoBadge.vue';
import { planosService } from '@/services/planosService';
import { assinaturasService } from '@/services/assinaturasService';
import cSwal from '@/utils/cSwal';
import { closeModal } from '@/utils/modalHelper';

export default {
  name: 'PlanoSelectorModal',
  components: {
    PlanoBadge
  },
  props: {
    modalId: {
      type: String,
      default: 'planoSelectorModal'
    },
    clinica: {
      type: Object,
      required: true
    },
    planoAtual: {
      type: Object,
      default: null
    },
    assinaturaAtual: {
      type: Object,
      default: null
    }
  },
  emits: ['plano-alterado'],
  data() {
    return {
      isLoading: false,
      planos: [],
      selectedPlano: null,
      dataInicio: '',
      motivoAlteracao: '',
    };
  },
  computed: {
    planosDisponiveis() {
      if (!Array.isArray(this.planos)) {
        return [];
      }
      return this.planos.filter(plano => plano.ativo);
    },
    
    minDate() {
      return new Date().toISOString().split('T')[0];
    },
    
    canConfirm() {
      return this.selectedPlano && 
             this.selectedPlano.id !== this.planoAtual?.id && 
             this.dataInicio;
    }
  },
  async mounted() {
    await this.loadPlanos();
    this.initializeData();
  },
  watch: {
    planoAtual: {
      handler() {
        this.initializeData();
      },
      immediate: false
    }
  },
  methods: {
    getNextBillingDate() {
      // Simular próxima data de cobrança (30 dias a partir de hoje)
      const nextDate = new Date();
      nextDate.setDate(nextDate.getDate() + 30);
      return nextDate.toLocaleDateString('pt-BR');
    },

    async loadPlanos() {
      this.isLoading = true;
      try {
        const response = await planosService.getPlanos({ ativo: true });
        // Garantir que sempre temos um array
        this.planos = Array.isArray(response) ? response : (response?.data || []);
        console.log('Planos carregados:', this.planos);
      } catch (error) {
        console.error('Erro ao carregar planos:', error);
        this.planos = []; // Garantir que seja um array vazio em caso de erro
        cSwal.cError('Erro ao carregar planos disponíveis.');
      }
      this.isLoading = false;
    },
    
    initializeData() {
      // Definir data de início padrão (hoje)
      this.dataInicio = new Date().toISOString().split('T')[0];

      // Selecionar plano atual por padrão
      if (this.planoAtual && this.planoAtual.id) {
        this.selectedPlano = this.planoAtual;
      } else {
        this.selectedPlano = null;
      }
    },
    
    selectPlano(plano) {
      this.selectedPlano = plano;
    },
    
    getPlanoIcon(plano) {
      if (plano.modulo_ortodontia) return 'fas fa-tooth';
      return 'fas fa-hospital-building';
    },
    
    formatCurrency(value) {
      return planosService.formatCurrency(value);
    },
    
    formatQuantity(value) {
      return planosService.formatQuantity(value);
    },
    
    async confirmarAlteracao() {
      if (!this.canConfirm) return;
      
      const confirmText = `
        <div class="text-start">
          <p><strong>Plano atual:</strong> ${this.planoAtual?.nome || 'Nenhum'}</p>
          <p><strong>Novo plano:</strong> ${this.selectedPlano.nome}</p>
          <p><strong>Data de início:</strong> ${new Date(this.dataInicio).toLocaleDateString('pt-BR')}</p>
          ${this.motivoAlteracao ? `<p><strong>Motivo:</strong> ${this.motivoAlteracao}</p>` : ''}
        </div>
      `;
      
      cSwal.cConfirm(
        'Confirmar alteração de plano?',
        async () => {
          await this.processarAlteracao();
        },
        'Sim, alterar plano',
        'Cancelar',
        confirmText
      );
    },
    
    async processarAlteracao() {
      cSwal.loading('Alterando plano...');
      
      try {
        const resultado = await assinaturasService.alterarPlano(
          this.clinica.id,
          this.selectedPlano.id,
          this.dataInicio,
          this.motivoAlteracao || null
        );
        
        cSwal.loaded();
        cSwal.cSuccess('Plano alterado com sucesso!');

        // Fechar modal usando modalHelper
        closeModal(this.modalId);

        // Emitir evento para atualizar dados na tela pai
        this.$emit('plano-alterado', {
          planoAnterior: this.planoAtual,
          novoPlano: this.selectedPlano,
          assinatura: resultado.nova_assinatura
        });
        
      } catch (error) {
        cSwal.loaded();
        console.error('Erro ao alterar plano:', error);
        
        if (error.response?.status === 422) {
          const message = error.response.data.message || 'Erro de validação';
          cSwal.cError(message);
        } else {
          cSwal.cError('Erro ao alterar plano. Tente novamente.');
        }
      }
    }
  }
};
</script>

<style scoped>
.modal-title-container {
  flex: 1;
}

.modal-subtitle {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: normal;
}

.section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

.current-plan-section {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 0.75rem;
}

.billing-info {
  background: white;
  border-radius: 6px;
  padding: 0.75rem;
  border: 1px solid #e9ecef;
  height: 100%;
}

.billing-title {
  font-size: 0.8rem;
  color: #495057;
  margin-bottom: 0.375rem;
  font-weight: 600;
}

.billing-date {
  font-size: 1rem;
  font-weight: 600;
  color: #28a745;
  margin-bottom: 0.125rem;
}

.billing-amount {
  font-size: 1.1rem;
  font-weight: 700;
  color: #007bff;
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 0.75rem;
}

.plan-card {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.plan-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.plan-card--selected {
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.plan-card--current {
  border-color: #ffc107;
}

.plan-card-header {
  position: relative;
  padding: 0.375rem 0.5rem;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.plan-icon {
  font-size: 1.2rem;
}

.plan-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.plan-card-body {
  padding: 0.5rem;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.plan-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.125rem;
}

.plan-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.375rem;
}

.price-period {
  font-size: 0.75rem;
  font-weight: 400;
  color: #718096;
}

.plan-features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.features-column {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.7rem;
  color: #4a5568;
}

.feature-item i {
  width: 16px;
  color: #718096;
}

.plan-card-footer {
  padding: 0.375rem 0.5rem;
  background: #f8f9fa;
  display: flex;
  justify-content: center;
}

.selection-indicator {
  color: #007bff;
  font-size: 1.25rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.plan-card--selected .selection-indicator {
  opacity: 1;
}

.change-settings {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.form-control {
  border-radius: 6px;
  border: 1px solid #ced4da;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
  border-radius: 6px;
  font-weight: 500;
  padding: 0.5rem 1rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
