<template>
  <div class="plano-badge-container">
    <!-- Badge do Plano -->
    <div 
      class="plano-badge" 
      :class="badgeClass"
      :style="badgeStyle"
      @click="$emit('click')"
    >
      <div class="plano-badge-content">
        <div class="plano-icon">
          <i :class="planoIcon"></i>
        </div>
        <div class="plano-info">
          <div class="plano-nome">{{ planoNome }}</div>
          <div class="plano-valor" v-if="planoValor">{{ planoValor }}</div>
        </div>
        <div class="plano-status" v-if="showStatus">
          <span class="status-indicator" :class="statusClass"></span>
          <span class="status-text">{{ statusText }}</span>
        </div>
      </div>
      
      <!-- Indicador de ação se clicável -->
      <div class="action-indicator" v-if="clickable">
        <i class="fas fa-chevron-right"></i>
      </div>
    </div>

    <!-- Informações adicionais (opcional) -->
    <div class="plano-details" v-if="showDetails && assinatura && assinatura.data_inicio">
      <div class="detail-item" v-if="assinatura.data_inicio && assinatura.data_fim">
        <span class="detail-label">Vigência:</span>
        <span class="detail-value">{{ formatDateRange(assinatura.data_inicio, assinatura.data_fim) }}</span>
      </div>
      <div class="detail-item" v-if="diasRestantes !== null && diasRestantes !== undefined">
        <span class="detail-label">Dias restantes:</span>
        <span class="detail-value" :class="diasRestantesClass">{{ diasRestantes }} dias</span>
      </div>
      <div class="detail-item" v-if="proximaCobranca">
        <span class="detail-label">Próxima cobrança:</span>
        <span class="detail-value">{{ formatDate(proximaCobranca) }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { assinaturasService } from '@/services/assinaturasService';

export default {
  name: 'PlanoBadge',
  props: {
    plano: {
      type: Object,
      default: null
    },
    assinatura: {
      type: Object,
      default: null
    },
    size: {
      type: String,
      default: 'medium', // small, medium, large
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    variant: {
      type: String,
      default: 'default', // default, compact, detailed
      validator: value => ['default', 'compact', 'detailed'].includes(value)
    },
    clickable: {
      type: Boolean,
      default: false
    },
    showStatus: {
      type: Boolean,
      default: true
    },
    showDetails: {
      type: Boolean,
      default: false
    }
  },
  emits: ['click'],
  computed: {
    planoNome() {
      return this.plano?.nome || 'Sem Plano';
    },
    
    planoValor() {
      if (!this.plano?.valor_mensal) return null;
      return assinaturasService.formatCurrency(this.plano.valor_mensal);
    },
    
    planoIcon() {
      if (!this.plano) return 'fas fa-question-circle';
      
      // Ícones baseados no tipo de plano ou características
      if (this.plano.modulo_ortodontia) return 'fas fa-tooth';
      return 'fas fa-hospital-building';
    },
    
    badgeClass() {
      return [
        `plano-badge--${this.size}`,
        `plano-badge--${this.variant}`,
        {
          'plano-badge--clickable': this.clickable,
          'plano-badge--no-plano': !this.plano
        }
      ];
    },
    
    badgeStyle() {
      if (!this.plano?.cor) return {};

      try {
        return {
          '--plano-color': this.plano.cor,
          '--plano-color-light': this.lightenColor(this.plano.cor, 0.9),
          '--plano-color-dark': this.darkenColor(this.plano.cor, 0.2)
        };
      } catch (error) {
        console.warn('Erro ao processar cor do plano:', error);
        return {};
      }
    },
    
    statusText() {
      if (!this.assinatura?.status) return 'Inativo';

      try {
        const statusInfo = assinaturasService.formatStatus(this.assinatura.status);
        return statusInfo?.text || 'Inativo';
      } catch (error) {
        console.warn('Erro ao formatar status:', error);
        return 'Inativo';
      }
    },

    statusClass() {
      if (!this.assinatura?.status) return 'status--inactive';

      try {
        const statusInfo = assinaturasService.formatStatus(this.assinatura.status);
        return `status--${statusInfo?.color || 'inactive'}`;
      } catch (error) {
        console.warn('Erro ao formatar classe do status:', error);
        return 'status--inactive';
      }
    },
    
    diasRestantes() {
      if (!this.assinatura?.data_fim) return null;

      try {
        return assinaturasService.calcularDiasRestantes(this.assinatura.data_fim);
      } catch (error) {
        console.warn('Erro ao calcular dias restantes:', error);
        return null;
      }
    },

    diasRestantesClass() {
      if (this.diasRestantes === null) return '';

      if (this.diasRestantes <= 7) return 'text-danger';
      if (this.diasRestantes <= 30) return 'text-warning';
      return 'text-success';
    },

    proximaCobranca() {
      if (!this.assinatura?.dia_cobranca) return null;

      try {
        return assinaturasService.calcularProximaCobranca(this.assinatura.dia_cobranca);
      } catch (error) {
        console.warn('Erro ao calcular próxima cobrança:', error);
        return null;
      }
    }
  },
  methods: {
    formatDate(date) {
      if (!date) return '';
      return new Date(date).toLocaleDateString('pt-BR');
    },
    
    formatDateRange(inicio, fim) {
      if (!inicio || !fim) return '';
      return `${this.formatDate(inicio)} - ${this.formatDate(fim)}`;
    },
    
    lightenColor(color, amount) {
      const usePound = color[0] === '#';
      const col = usePound ? color.slice(1) : color;
      const num = parseInt(col, 16);
      let r = (num >> 16) + amount * 255;
      let g = (num >> 8 & 0x00FF) + amount * 255;
      let b = (num & 0x0000FF) + amount * 255;
      r = r > 255 ? 255 : r < 0 ? 0 : r;
      g = g > 255 ? 255 : g < 0 ? 0 : g;
      b = b > 255 ? 255 : b < 0 ? 0 : b;
      return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
    },
    
    darkenColor(color, amount) {
      const usePound = color[0] === '#';
      const col = usePound ? color.slice(1) : color;
      const num = parseInt(col, 16);
      let r = (num >> 16) - amount * 255;
      let g = (num >> 8 & 0x00FF) - amount * 255;
      let b = (num & 0x0000FF) - amount * 255;
      r = r > 255 ? 255 : r < 0 ? 0 : r;
      g = g > 255 ? 255 : g < 0 ? 0 : g;
      b = b > 255 ? 255 : b < 0 ? 0 : b;
      return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
    }
  }
};
</script>

<style scoped>
.plano-badge-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.plano-badge {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, var(--plano-color-light, #f8f9fa), var(--plano-color-light, #f8f9fa));
  border: 2px solid var(--plano-color, #dee2e6);
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.plano-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--plano-color, #dee2e6);
}

.plano-badge--clickable {
  cursor: pointer;
}

.plano-badge--clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--plano-color-dark, #adb5bd);
}

.plano-badge--no-plano {
  background: #f8f9fa;
  border-color: #dee2e6;
  opacity: 0.7;
}

.plano-badge-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.plano-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background: var(--plano-color, #6c757d);
  color: white;
  border-radius: 50%;
  font-size: 1.1rem;
}

.plano-info {
  flex: 1;
}

.plano-nome {
  font-weight: 600;
  font-size: 1rem;
  color: #2d3748;
  margin-bottom: 0.125rem;
}

.plano-valor {
  font-size: 0.875rem;
  color: #718096;
  font-weight: 500;
}

.plano-status {
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status--success { background-color: #48bb78; }
.status--warning { background-color: #ed8936; }
.status--danger { background-color: #f56565; }
.status--info { background-color: #4299e1; }
.status--secondary { background-color: #a0aec0; }
.status--inactive { background-color: #e2e8f0; }

.status-text {
  font-size: 0.75rem;
  font-weight: 500;
  color: #4a5568;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.action-indicator {
  color: #a0aec0;
  font-size: 0.875rem;
  margin-left: 0.5rem;
}

/* Variações de tamanho */
.plano-badge--small {
  padding: 0.5rem 0.75rem;
}

.plano-badge--small .plano-icon {
  width: 2rem;
  height: 2rem;
  font-size: 0.875rem;
}

.plano-badge--small .plano-nome {
  font-size: 0.875rem;
}

.plano-badge--large {
  padding: 1rem 1.25rem;
}

.plano-badge--large .plano-icon {
  width: 3rem;
  height: 3rem;
  font-size: 1.25rem;
}

.plano-badge--large .plano-nome {
  font-size: 1.125rem;
}

/* Variação compacta */
.plano-badge--compact .plano-badge-content {
  gap: 0.5rem;
}

.plano-badge--compact .plano-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.plano-badge--compact .plano-valor {
  margin-bottom: 0;
}

/* Detalhes adicionais */
.plano-details {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.75rem;
  font-size: 0.875rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.375rem;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  color: #718096;
  font-weight: 500;
}

.detail-value {
  color: #2d3748;
  font-weight: 600;
}

.text-success { color: #48bb78 !important; }
.text-warning { color: #ed8936 !important; }
.text-danger { color: #f56565 !important; }
</style>
