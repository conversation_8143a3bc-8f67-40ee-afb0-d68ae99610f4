import axios from '@/services/axios'

export async function adicionarClinica(clinica) {
    try {
        const response = await axios.post('/clinicas', clinica)

        if (!response || !response.data)
            return false;

        return response.data

    } catch (error) {
        console.error('Erro ao adicionar clínica:', error);

        // Retornar erro específico se disponível
        if (error.response && error.response.data && error.response.data.message) {
            throw new Error(error.response.data.message);
        }

        throw error;
    }
}

export async function getClinicas() {
    try {
        const response = await axios.get('/clinicas')

        if (!response || !response.data)
            return false;

        return response.data

    } catch (error) {
        console.error('Erro ao consultar clínicas:', error);
    }

    return false

}

export async function getClinicasWithCounts() {
    try {
        const response = await axios.get('/clinicas/with-counts')

        if (!response || !response.data)
            return false;

        return response.data

    } catch (error) {
        console.error('Erro ao consultar clínicas com contadores:', error);
    }

    return false

}

export async function searchClinicas(search = '') {
    try {
        const response = await axios.post('/clinicas/search', {
            search: search
        })

        if (!response || !response.data)
            return [];

        return response.data

    } catch (error) {
        console.error('Erro ao buscar clínicas:', error);
    }

    return []

}

export async function getClinica(id) {
    try {
        const response = await axios.get(`/clinicas/${id}`)

        if (!response || !response.data)
            return false;

        return response.data

    } catch (error) {
        console.error('Erro ao consultar clínica:', error);
    }

    return false

}

export async function updateClinica(clinica) {
    try {
        const response = await axios.put(`/clinicas/${clinica.id}`, clinica)

        if (!response || response.status !== 200)
            return false;

        return response.data

    } catch (error) {
        console.error('Erro ao atualizar clínica:', error);
    }

    return false
}

export async function updateClinicaField(clinicaId, field, value) {
    try {
        const data = {};
        data[field] = value;

        const response = await axios.patch(`/clinicas/${clinicaId}`, data);

        if (!response || response.status !== 200) {
            return false;
        }

        return response.data;
    } catch (error) {
        console.error('Erro ao atualizar campo da clínica:', error);
        return false;
    }
}

export async function createTrialAccount(trialData) {
    try {
        const response = await axios.post('/trial-signup', trialData);

        if (!response || !response.data)
            return { success: false, message: 'Resposta inválida do servidor' };

        return response.data;

    } catch (error) {
        console.error('Erro ao criar conta trial:', error);

        if (error.response && error.response.data && error.response.data.message) {
            throw new Error(error.response.data.message);
        }

        throw error;
    }
}
