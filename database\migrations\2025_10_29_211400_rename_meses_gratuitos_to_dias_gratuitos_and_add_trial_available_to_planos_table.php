<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('planos', function (Blueprint $table) {
            $table->renameColumn('meses_gratuitos', 'dias_gratuitos');
            $table->boolean('trial_available')->default(false)->after('dias_gratuitos');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('planos', function (Blueprint $table) {
            $table->dropColumn('trial_available');
            $table->renameColumn('dias_gratuitos', 'meses_gratuitos');
        });
    }
};
