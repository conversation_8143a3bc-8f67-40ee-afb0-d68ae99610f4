<template>
  <div class="faturas-clinica">
    <!-- Header com estatísticas -->
    <div class="row mb-4">
      <div class="col-12">
        <div class="card">
          <div class="card-header pb-0">
            <div class="d-flex justify-content-between align-items-center">
              <h6 class="mb-0">
                <i class="fas fa-file-invoice-dollar text-primary me-2"></i>
                Faturas da Clínica
              </h6>
              <button 
                class="btn btn-primary btn-sm"
                @click="criarNovaFatura"
              >
                <i class="fas fa-plus me-1"></i>
                Nova Fatura
              </button>
            </div>
          </div>
          <div class="card-body">
            <!-- Estatísticas rápidas -->
            <div class="row g-3 mb-3">
              <div class="col-md-3">
                <div class="stat-card stat-card--warning">
                  <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ estatisticas.pendentes || 0 }}</div>
                    <div class="stat-label">Pendentes</div>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="stat-card stat-card--success">
                  <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ estatisticas.pagas || 0 }}</div>
                    <div class="stat-label">Pagas</div>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="stat-card stat-card--danger">
                  <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ estatisticas.vencidas || 0 }}</div>
                    <div class="stat-label">Vencidas</div>
                  </div>
                </div>
              </div>
              <div class="col-md-3">
                <div class="stat-card stat-card--info">
                  <div class="stat-icon">
                    <i class="fas fa-dollar-sign"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ formatCurrency(estatisticas.valor_total || 0) }}</div>
                    <div class="stat-label">Valor Total</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filtros -->
    <div class="row mb-3">
      <div class="col-md-3">
        <div class="form-group">
          <label class="form-label">Status</label>
          <select class="form-select" v-model="filtros.status" @change="loadFaturas">
            <option value="">Todos os status</option>
            <option value="pendente">Pendente</option>
            <option value="pago">Pago</option>
            <option value="vencido">Vencido</option>
            <option value="cancelado">Cancelado</option>
          </select>
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-group">
          <label class="form-label">Período (Vencimento)</label>
          <input 
            type="month" 
            class="form-control" 
            v-model="filtros.mes_vencimento" 
            @change="loadFaturas"
          >
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-group">
          <label class="form-label">Valor Mínimo</label>
          <input 
            type="number" 
            class="form-control" 
            v-model="filtros.valor_min" 
            placeholder="0,00"
            step="0.01"
            @change="loadFaturas"
          >
        </div>
      </div>
      <div class="col-md-3">
        <div class="form-group">
          <label class="form-label">Valor Máximo</label>
          <input 
            type="number" 
            class="form-control" 
            v-model="filtros.valor_max" 
            placeholder="0,00"
            step="0.01"
            @change="loadFaturas"
          >
        </div>
      </div>
    </div>

    <!-- Lista de Faturas -->
    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-header pb-0">
            <h6 class="mb-0">Lista de Faturas</h6>
          </div>
          <div class="card-body p-0">
            <!-- Tabela de faturas -->
            <div class="table-responsive">
              <table class="table align-items-center mb-0">
                <thead>
                  <tr>
                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Descrição
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Valor
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Vencimento
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Status
                    </th>
                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-if="isLoading">
                    <td colspan="5" class="text-center py-4">
                      <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Carregando...</span>
                      </div>
                    </td>
                  </tr>
                  <tr v-else-if="filteredFaturas.length === 0">
                    <td colspan="5" class="text-center py-4 text-muted">
                      {{ faturas.length === 0 ? 'Nenhuma fatura encontrada para esta clínica' : 'Nenhuma fatura corresponde aos filtros aplicados' }}
                    </td>
                  </tr>
                  <tr v-else v-for="fatura in filteredFaturas" :key="fatura.id">
                    <td>
                      <div class="d-flex px-2 py-1">
                        <div class="d-flex flex-column justify-content-center">
                          <h6 class="mb-0 text-sm">{{ fatura.descricao }}</h6>
                          <p class="text-xs text-secondary mb-0" v-if="fatura.observacoes">
                            {{ fatura.observacoes }}
                          </p>
                          <small v-if="fatura.numero_fatura" class="text-muted">
                            #{{ fatura.numero_fatura }}
                          </small>
                        </div>
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">
                        {{ formatCurrency(fatura.valor_final) }}
                      </span>
                      <div v-if="fatura.valor_final !== fatura.valor_nominal" class="text-xs text-muted">
                        Original: {{ formatCurrency(fatura.valor_nominal) }}
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="text-secondary text-xs font-weight-bold">
                        {{ formatDate(fatura.data_vencimento) }}
                      </span>
                      <div :class="getVencimentoClass(fatura)" class="text-xs">
                        {{ getVencimentoText(fatura) }}
                      </div>
                    </td>
                    <td class="align-middle text-center">
                      <span class="badge badge-sm" :class="getStatusBadgeClass(fatura.status)">
                        {{ formatStatus(fatura.status) }}
                      </span>
                    </td>
                    <td class="align-middle text-center">
                      <div class="btn-group btn-group-sm">
                        <button
                          class="btn btn-outline-primary btn-sm"
                          @click="visualizarFatura(fatura)"
                          title="Visualizar detalhes"
                        >
                          <i class="fas fa-eye"></i>
                        </button>
                        <button
                          v-if="fatura.status === 'pendente'"
                          class="btn btn-outline-success btn-sm"
                          @click="marcarComoPaga(fatura)"
                          title="Marcar como paga"
                        >
                          <i class="fas fa-check"></i>
                        </button>
                        <button
                          v-if="fatura.status !== 'pago'"
                          class="btn btn-outline-warning btn-sm"
                          @click="editarFatura(fatura)"
                          title="Editar fatura"
                        >
                          <i class="fas fa-edit"></i>
                        </button>
                        <button
                          v-if="fatura.status !== 'pago'"
                          class="btn btn-outline-danger btn-sm"
                          @click="cancelarFatura(fatura)"
                          title="Cancelar fatura"
                        >
                          <i class="fas fa-times"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Paginação -->
            <div v-if="pagination.total > pagination.per_page" class="d-flex justify-content-center mt-3">
              <nav>
                <ul class="pagination pagination-sm">
                  <li class="page-item" :class="{ disabled: pagination.current_page === 1 }">
                    <button class="page-link" @click="changePage(pagination.current_page - 1)">
                      <i class="fas fa-chevron-left"></i>
                    </button>
                  </li>
                  <li 
                    v-for="page in visiblePages" 
                    :key="page"
                    class="page-item" 
                    :class="{ active: page === pagination.current_page }"
                  >
                    <button class="page-link" @click="changePage(page)">{{ page }}</button>
                  </li>
                  <li class="page-item" :class="{ disabled: pagination.current_page === pagination.last_page }">
                    <button class="page-link" @click="changePage(pagination.current_page + 1)">
                      <i class="fas fa-chevron-right"></i>
                    </button>
                  </li>
                </ul>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { faturasClinicasService } from '@/services/faturasClinicasService';
import cSwal from '@/utils/cSwal';

export default {
  name: 'FaturasClinica',
  props: {
    clinica: {
      type: Object,
      required: true
    }
  },
  emits: ['fatura-alterada'],
  data() {
    return {
      faturas: [],
      estatisticas: {},
      isLoading: false,
      filtros: {
        status: '',
        mes_vencimento: '',
        valor_min: '',
        valor_max: ''
      },
      pagination: {
        current_page: 1,
        last_page: 1,
        per_page: 10,
        total: 0
      }
    };
  },
  computed: {
    filteredFaturas() {
      let filtered = [...this.faturas];

      // Filtrar por status (se algum status estiver selecionado)
      if (this.filtros.status && this.filtros.status !== 'todos') {
        filtered = filtered.filter(fatura => fatura.status === this.filtros.status);
      }

      // Filtrar por mês de vencimento
      if (this.filtros.mes_vencimento) {
        filtered = filtered.filter(fatura => {
          const dataVencimento = new Date(fatura.data_vencimento);
          const mesVencimento = dataVencimento.getFullYear() + '-' + String(dataVencimento.getMonth() + 1).padStart(2, '0');
          return mesVencimento === this.filtros.mes_vencimento;
        });
      }

      // Filtrar por valor mínimo
      if (this.filtros.valor_min) {
        filtered = filtered.filter(fatura => parseFloat(fatura.valor_final) >= parseFloat(this.filtros.valor_min));
      }

      // Filtrar por valor máximo
      if (this.filtros.valor_max) {
        filtered = filtered.filter(fatura => parseFloat(fatura.valor_final) <= parseFloat(this.filtros.valor_max));
      }

      return filtered;
    },

    visiblePages() {
      const current = this.pagination.current_page;
      const last = this.pagination.last_page;
      const delta = 2;
      const range = [];
      
      for (let i = Math.max(2, current - delta); i <= Math.min(last - 1, current + delta); i++) {
        range.push(i);
      }
      
      if (current - delta > 2) {
        range.unshift('...');
      }
      if (current + delta < last - 1) {
        range.push('...');
      }
      
      range.unshift(1);
      if (last !== 1) {
        range.push(last);
      }
      
      return range;
    }
  },
  async mounted() {
    await this.loadFaturas();
    await this.loadEstatisticas();
  },
  methods: {
    async loadFaturas() {
      this.isLoading = true;
      try {
        const params = {
          page: this.pagination.current_page,
          per_page: this.pagination.per_page,
          ...this.filtros
        };
        
        const response = await faturasClinicasService.getFaturas(this.clinica.id, params);
        this.faturas = response.data || [];
        // A resposta do Laravel já tem a estrutura de paginação
        this.pagination = {
          current_page: response.current_page || 1,
          last_page: response.last_page || 1,
          per_page: response.per_page || 10,
          total: response.total || 0
        };
      } catch (error) {
        console.error('Erro ao carregar faturas:', error);
        cSwal.cError('Erro ao carregar faturas.');
      }
      this.isLoading = false;
    },
    
    async loadEstatisticas() {
      try {
        this.estatisticas = await faturasClinicasService.getEstatisticas(this.clinica.id);
      } catch (error) {
        console.error('Erro ao carregar estatísticas:', error);
      }
    },
    
    changePage(page) {
      if (page >= 1 && page <= this.pagination.last_page) {
        this.pagination.current_page = page;
        this.loadFaturas();
      }
    },
    
    formatCurrency(value) {
      return faturasClinicasService.formatCurrency(value);
    },
    
    formatDate(date) {
      return faturasClinicasService.formatDate(date);
    },
    
    formatStatus(status) {
      return faturasClinicasService.formatStatus(status);
    },
    
    getStatusBadgeClass(status) {
      const color = faturasClinicasService.getStatusColor(status);
      return `bg-${color}`;
    },
    
    getVencimentoClass(fatura) {
      if (faturasClinicasService.isVencida(fatura)) {
        return 'text-danger';
      } else if (faturasClinicasService.calcularDiasAteVencimento(fatura.data_vencimento) <= 7) {
        return 'text-warning';
      } else {
        return 'text-success';
      }
    },
    
    getVencimentoText(fatura) {
      if (faturasClinicasService.isVencida(fatura)) {
        const dias = faturasClinicasService.calcularDiasEmAtraso(fatura.data_vencimento);
        return `${dias} dias em atraso`;
      } else {
        const dias = faturasClinicasService.calcularDiasAteVencimento(fatura.data_vencimento);
        return `${dias} dias restantes`;
      }
    },
    
    criarNovaFatura() {
      // Implementar modal de criação de fatura
      console.log('Criar nova fatura para clínica:', this.clinica.id);
    },
    
    visualizarFatura(fatura) {
      // Implementar modal de detalhes da fatura
      console.log('Visualizar fatura:', fatura);
    },
    
    editarFatura(fatura) {
      // Implementar modal de edição de fatura
      console.log('Editar fatura:', fatura);
    },
    
    async marcarComoPaga(fatura) {
      const confirmText = `
        <div class="text-start">
          <p><strong>Fatura:</strong> ${fatura.numero_fatura}</p>
          <p><strong>Valor:</strong> ${this.formatCurrency(fatura.valor_final)}</p>
          <p><strong>Vencimento:</strong> ${this.formatDate(fatura.data_vencimento)}</p>
        </div>
      `;
      
      cSwal.cConfirm(
        'Marcar fatura como paga?',
        async () => {
          await this.processarPagamento(fatura);
        },
        'Sim, marcar como paga',
        'Cancelar',
        confirmText
      );
    },
    
    async processarPagamento(fatura) {
      cSwal.loading('Processando pagamento...');
      
      try {
        await faturasClinicasService.pagarFatura(this.clinica.id, fatura.id, {
          forma_pagamento: 'manual', // Pode ser alterado para um modal de seleção
          data_pagamento: new Date().toISOString().split('T')[0]
        });
        
        cSwal.loaded();
        cSwal.cSuccess('Fatura marcada como paga!');
        
        // Recarregar dados
        await this.loadFaturas();
        await this.loadEstatisticas();
        
        // Emitir evento para atualizar dados na tela pai
        this.$emit('fatura-alterada', {
          tipo: 'pagamento',
          fatura: fatura
        });
        
      } catch (error) {
        cSwal.loaded();
        console.error('Erro ao marcar fatura como paga:', error);
        
        if (error.response?.status === 422) {
          const message = error.response.data.message || 'Erro de validação';
          cSwal.cError(message);
        } else {
          cSwal.cError('Erro ao processar pagamento. Tente novamente.');
        }
      }
    },
    
    async cancelarFatura(fatura) {
      const confirmText = `
        <div class="text-start">
          <p><strong>Fatura:</strong> ${fatura.numero_fatura}</p>
          <p><strong>Valor:</strong> ${this.formatCurrency(fatura.valor_final)}</p>
          <p class="text-warning"><i class="fas fa-exclamation-triangle me-1"></i> Esta ação não pode ser desfeita.</p>
        </div>
      `;
      
      cSwal.cConfirm(
        'Cancelar fatura?',
        async () => {
          await this.processarCancelamento(fatura);
        },
        'Sim, cancelar',
        'Não cancelar',
        confirmText
      );
    },
    
    async processarCancelamento(fatura) {
      cSwal.loading('Cancelando fatura...');
      
      try {
        await faturasClinicasService.cancelarFatura(this.clinica.id, fatura.id);
        
        cSwal.loaded();
        cSwal.cSuccess('Fatura cancelada com sucesso!');
        
        // Recarregar dados
        await this.loadFaturas();
        await this.loadEstatisticas();
        
        // Emitir evento para atualizar dados na tela pai
        this.$emit('fatura-alterada', {
          tipo: 'cancelamento',
          fatura: fatura
        });
        
      } catch (error) {
        cSwal.loaded();
        console.error('Erro ao cancelar fatura:', error);
        
        if (error.response?.status === 422) {
          const message = error.response.data.message || 'Erro de validação';
          cSwal.cError(message);
        } else {
          cSwal.cError('Erro ao cancelar fatura. Tente novamente.');
        }
      }
    }
  }
};
</script>

<style scoped>
.stat-card {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-radius: 8px;
  background: white;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card--success { border-left: 4px solid #28a745; }
.stat-card--warning { border-left: 4px solid #ffc107; }
.stat-card--danger { border-left: 4px solid #dc3545; }
.stat-card--info { border-left: 4px solid #17a2b8; }

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  margin-right: 1rem;
  font-size: 1.25rem;
}

.stat-card--success .stat-icon {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
}

.stat-card--warning .stat-icon {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.stat-card--danger .stat-icon {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.stat-card--info .stat-icon {
  background: rgba(23, 162, 184, 0.1);
  color: #17a2b8;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #718096;
  font-weight: 500;
}

.empty-state {
  padding: 2rem;
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.table th {
  font-weight: 600;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.fatura-description,
.vencimento-info,
.valor-info {
  line-height: 1.2;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
}

.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}
</style>
