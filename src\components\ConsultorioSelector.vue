<template>
  <div class="consultorio-selector">
    <!-- Header com título -->
    <div class="selector-header">
      <h5 class="selector-title">
        <i class="fas fa-clinic-medical me-2"></i>
        Consultórios
      </h5>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-state">
      <div class="spinner-border spinner-border-sm text-primary" role="status">
        <span class="visually-hidden">Carregando...</span>
      </div>
      <small class="text-muted ms-2">Carregando consultórios...</small>
    </div>

    <!-- Lista de Consultórios -->
    <div v-else class="consultorios-grid">
      <div
        v-for="consultorio in consultorios"
        :key="consultorio.id"
        class="consultorio-card"
        :class="{ 
          'active': selectedConsultorioId === consultorio.id,
          'inactive': !consultorio.ativo 
        }"
        @click="selectConsultorio(consultorio)"
        :style="{ '--consultorio-color': consultorio.cor }"
      >
        <!-- Indicador de seleção -->
        <div class="selection-indicator">
          <i class="fas fa-check"></i>
        </div>

        <!-- Ícone do consultório -->
        <div class="consultorio-icon">
          <i :class="consultorio.icone"></i>
        </div>

        <!-- Nome e descrição -->
        <div class="consultorio-info">
          <h6 class="consultorio-name">{{ consultorio.nome }}</h6>
          <p v-if="consultorio.descricao" class="consultorio-description">
            {{ consultorio.descricao }}
          </p>
        </div>

        <!-- Botões de ação -->
        <div class="consultorio-actions">
          <button
            type="button"
            class="btn btn-sm btn-outline-secondary action-btn"
            @click.stop="$emit('edit-consultorio', consultorio)"
            :title="`Editar ${consultorio.nome}`"
          >
            <i class="fas fa-edit"></i>
          </button>
        </div>

        <!-- Badge de status -->
        <div v-if="!consultorio.ativo" class="status-badge inactive-badge">
          Inativo
        </div>
      </div>

      <!-- Card para adicionar novo consultório -->
      <div
        class="consultorio-card add-card"
        @click="$emit('add-consultorio')"
      >
        <div class="add-icon">
          <i class="fas fa-plus"></i>
        </div>
        <div class="add-text">
          <h6>Adicionar consultório</h6>
          <p>Criar nova agenda</p>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="!isLoading && consultorios.length === 0" class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-clinic-medical"></i>
      </div>
      <h6>Nenhum consultório encontrado</h6>
      <p class="text-muted">Adicione seu primeiro consultório para começar</p>
      <button
        type="button"
        class="btn btn-primary btn-sm"
        @click="$emit('add-consultorio')"
      >
        <i class="fas fa-plus me-1"></i>
        Adicionar consultório
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConsultorioSelector',
  props: {
    consultorios: {
      type: Array,
      default: () => []
    },
    selectedConsultorioId: {
      type: [Number, String],
      default: null
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },
  emits: [
    'consultorio-selected',
    'add-consultorio',
    'edit-consultorio'
  ],
  methods: {
    selectConsultorio(consultorio) {
      if (!consultorio.ativo) return;
      
      if (this.selectedConsultorioId !== consultorio.id) {
        this.$emit('consultorio-selected', consultorio);
      }
    }
  }
};
</script>

<style scoped>
.consultorio-selector {
  background: #ffffff;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

/* Header */
.selector-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e9ecef;
}

.selector-title {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
}

.add-consultorio-btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.add-consultorio-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #6c757d;
}

/* Grid de Consultórios */
.consultorios-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 0.6rem;
}

/* Card do Consultório */
.consultorio-card {
  position: relative;
  background: #ffffff;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  min-height: 90px;
  display: flex;
  flex-direction: column;
}

.consultorio-card:hover {
  border-color: var(--consultorio-color, #007bff);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.consultorio-card.active {
  border-color: var(--consultorio-color, #007bff);
  background: linear-gradient(135deg, 
    rgba(var(--consultorio-color-rgb, 0, 123, 255), 0.05) 0%, 
    rgba(var(--consultorio-color-rgb, 0, 123, 255), 0.02) 100%);
  box-shadow: 0 4px 20px rgba(var(--consultorio-color-rgb, 0, 123, 255), 0.2);
}

.consultorio-card.inactive {
  opacity: 0.6;
  cursor: not-allowed;
}

.consultorio-card.inactive:hover {
  transform: none;
  border-color: #e9ecef;
}

/* Indicador de seleção */
.selection-indicator {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 20px;
  height: 20px;
  background: var(--consultorio-color, #007bff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.7rem;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.consultorio-card.active .selection-indicator {
  opacity: 1;
  transform: scale(1);
}

/* Ícone do consultório */
.consultorio-icon {
  width: 32px;
  height: 30px;
  background: 
  linear-gradient(135deg, rgba(0, 0, 0, 0.1), rgba(255,255,255, 0.2) 80%),
  var(--consultorio-color, #007bff);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
  margin-bottom: 0.25rem;
  transition: all 0.3s ease;
}

.consultorio-card:hover .consultorio-icon {
  transform: scale(1.1);
}

/* Informações do consultório */
.consultorio-info {
  margin-bottom: 0;
  flex: 1;
}

.consultorio-name {
  margin: 0 0 0.125rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: #495057;
  line-height: 1.2;
}

.consultorio-description {
  margin: 0;
  font-size: 0.75rem;
  color: #6c757d;
  line-height: 1.2;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Ações do consultório */
.consultorio-actions {
  position: absolute;
  bottom: 0.5rem;
  right: 0.5rem;
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.consultorio-card:hover .consultorio-actions {
  opacity: 1;
  transform: scale(1);
}

.action-btn {
  border-radius: 4px;
  padding: 0.2rem 0.35rem;
  font-size: 0.7rem;
  min-width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Badge de status */
.status-badge {
  position: absolute;
  top: 0.5rem;
  left: 0.5rem;
  padding: 0.2rem 0.4rem;
  border-radius: 8px;
  font-size: 0.65rem;
  font-weight: 600;
  text-transform: uppercase;
}

.inactive-badge {
  background: #dc3545;
  color: white;
}

/* Card para adicionar */
.add-card {
  border: 2px dashed #dee2e6;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 90px;
}

.add-card:hover {
  border-color: #007bff;
  background: rgba(0, 123, 255, 0.05);
}

.add-icon {
  width: 36px;
  height: 36px;
  background: #e9ecef;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.add-card:hover .add-icon {
  background: #007bff;
  color: white;
  transform: scale(1.1);
}

.add-text h6 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
}

.add-text p {
  margin: 0;
  font-size: 0.875rem;
  color: #6c757d;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.empty-icon {
  width: 64px;
  height: 64px;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 2rem;
  color: #dee2e6;
}

.empty-state h6 {
  margin-bottom: 0.5rem;
  color: #495057;
}

/* Responsive */
@media (max-width: 768px) {
  .consultorios-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 0.5rem;
  }

  .selector-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .add-consultorio-btn {
    width: 100%;
  }

  .consultorio-actions {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 480px) {
  .consultorios-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* Animações */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.consultorio-card {
  animation: fadeInUp 0.5s ease-out;
}

.consultorio-card:nth-child(1) { animation-delay: 0.1s; }
.consultorio-card:nth-child(2) { animation-delay: 0.2s; }
.consultorio-card:nth-child(3) { animation-delay: 0.3s; }
.consultorio-card:nth-child(4) { animation-delay: 0.4s; }
.consultorio-card:nth-child(5) { animation-delay: 0.5s; }
</style>
