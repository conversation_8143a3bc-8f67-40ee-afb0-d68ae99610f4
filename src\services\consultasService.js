import axios from '@/services/axios'

export async function getConsultas() {
    try {
        const response = await axios.get('/consultas');

        if (!response || !response.data)
            return false;

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao buscar consultas:', error);
    }

    return false;
}

export async function getConsultasByPaciente(paciente_id) {
    try {
        const response = await axios.get(`/consultas/paciente/${paciente_id}`);

        if (!response || !response.data)
            return false;

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao buscar consultas do paciente:', error);
    }

    return false;
}

export async function getConsultasByDentista(dentista_id) {
    try {
        const response = await axios.get(`/consultas/dentista/${dentista_id}`);

        if (!response || !response.data)
            return false;

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao buscar consultas do dentista:', error);
    }

    return false;
}

export async function getConsultasByData(data) {
    try {
        const response = await axios.get(`/consultas/data/${data}`);

        if (!response || !response.data)
            return false;

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao buscar consultas por data:', error);
    }

    return false;
}

export async function getConsultasByPeriodo(dataInicio, dataFim) {
    try {
        const response = await axios.get(`/consultas`, {
            params: {
                data_inicio: dataInicio,
                data_fim: dataFim
            }
        });

        if (!response || !response.data)
            return false;

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        } else {
            return response.data;
        }

    } catch (error) {
        console.error('Erro ao buscar consultas por período:', error);
        return false;
    }
}

export async function getConsulta(id) {
    try {
        const response = await axios.get(`/consultas/${id}`);

        if (!response || !response.data)
            return false;

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao buscar consulta:', error);
        return false;
    }
}

export async function novaConsulta(consulta) {
    try {
        // Se a consulta tem data e horário separados, vamos combiná-los
        if (consulta.data && consulta.horario) {
            // O backend espera um timestamp MySQL completo (YYYY-MM-DD HH:MM:SS)
            // Vamos combinar a data e o horário
            consulta.horario = `${consulta.data} ${consulta.horario}:00`;

            // Remove o campo data que não é esperado pelo backend
            delete consulta.data;
        }

        const response = await axios.post('/consultas', consulta);

        if (!response || !response.data)
            return false;

        if (response.data.status === 'error')
            return false;

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao criar nova consulta:', error);
    }

    return false;
}

export async function atualizarConsulta(id, consulta) {
    try {
        // Se a consulta tem data e horário separados, vamos combiná-los
        if (consulta.data && consulta.horario) {
            // O backend espera um timestamp MySQL completo (YYYY-MM-DD HH:MM:SS)
            // Vamos combinar a data e o horário
            consulta.horario = `${consulta.data} ${consulta.horario}:00`;

            // Remove o campo data que não é esperado pelo backend
            delete consulta.data;
        }

        const response = await axios.put(`/consultas/${id}`, consulta);

        if (!response || !response.data)
            return false;

        if (response.data.status === 'error')
            return false;

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao atualizar consulta:', error);
    }

    return false;
}

export async function atualizarStatusConsulta(id, status) {
    try {
        const response = await axios.patch(`/consultas/${id}/status`, { status });

        if (!response || !response.data)
            return false;

        if (response.data.status === 'error')
            return false;

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao atualizar status da consulta:', error);
    }

    return false;
}

export async function excluirConsulta(id) {
    try {
        const response = await axios.delete(`/consultas/${id}`);

        if (!response || !response.data)
            return false;

        if (response.data.status === 'error')
            return false;

        // Verificar se a resposta está no formato de API Resource do Laravel
        if (response.data.data) {
            return response.data.data;
        }

        return response.data;

    } catch (error) {
        console.error('Erro ao excluir consulta:', error);
    }

    return false;
}