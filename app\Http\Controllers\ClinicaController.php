<?php

namespace App\Http\Controllers;

use App\Models\Clinica;
use App\Models\Paciente;
use App\Traits\LogsActionHistory;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class ClinicaController extends Controller
{
    use LogsActionHistory;
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $clinicas = Clinica::all();

        return response()->json($clinicas);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Validar dados da clínica
        $request->validate([
            'nome' => 'required|string|max:255',
            'plano_id' => 'required|exists:planos,id',
            'observacoes' => 'nullable|string',
            'dentista.nome' => 'required|string|max:255',
            'dentista.email' => 'required|email|unique:users,email',
            'dentista.senha' => 'required|string|min:8',
        ]);

        try {
            return DB::transaction(function () use ($request) {
                // Gerar slug único baseado no nome
                $baseSlug = Str::slug(mb_strtolower(preg_replace('/[\s]/', '-', $request->nome)), '-');
                $slug = $this->generateUniqueSlug($baseSlug);

                // Criar clínica
                $clinica = Clinica::create([
                    'nome' => $request->nome,
                    'slug' => $slug,
                    'plano_id' => $request->plano_id,
                    'imagem_url' => $request->imagem_url,
                    'observacoes' => $request->observacoes,
                ]);

                // Criar usuário admin da clínica
                $user = \App\Models\User::create([
                    'name' => $request->input('dentista.nome'),
                    'username' => $this->generateUniqueUsername($request->input('dentista.email')),
                    'email' => $request->input('dentista.email'),
                    'password' => Hash::make($request->input('dentista.senha')),
                    'clinica_id' => $clinica->id,
                    'clinica_admin' => true,
                ]);

                // Criar dentista
                $dentista = \App\Models\Dentista::create([
                    'nome' => $request->input('dentista.nome'),
                    'clinica_id' => $clinica->id,
                    'user_id' => $user->id,
                    'ativo' => true,
                    'id_matricula' => \App\Models\MatriculaCounter::getNextIdMatricula($clinica->id),
                ]);

                // Log das ações
                $this->logCreateAction($clinica, null, $request, "Created new clinica: {$clinica->nome}");
                $this->logCreateAction($user, null, $request, "Created admin user for clinica: {$clinica->nome}");
                $this->logCreateAction($dentista, null, $request, "Created admin dentist for clinica: {$clinica->nome}");

                return responseSuccess([
                    'clinica' => $clinica,
                    'user' => $user,
                    'dentista' => $dentista
                ]);
            });
        } catch (\Exception $e) {
            Log::error('Erro ao criar clínica com dentista admin: ' . $e->getMessage());
            return responseError(['message' => 'Erro interno ao criar clínica. Tente novamente.']);
        }
    }

    /**
     * Gera um slug único adicionando números se necessário
     */
    private function generateUniqueSlug($baseSlug)
    {
        $slug = $baseSlug;
        $counter = 2;

        // Verifica se o slug já existe
        while (Clinica::where('slug', $slug)->exists()) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $clinica = Clinica::with(['pacientes', 'dentistas', 'users', 'plano'])
            ->withCount([
                'pacientes',
                'dentistas',
                'users'
            ])
            ->findOrFail($id);

        // Contar mentorias através dos pacientes da clínica
        $mentoriasCount = \App\Models\Mentoria::whereHas('paciente', function($query) use ($id) {
            $query->where('clinica_id', $id);
        })->count();

        // Contar imagens através dos pacientes da clínica
        $imagensCount = \App\Models\Imagem::whereHas('paciente', function($query) use ($id) {
            $query->where('clinica_id', $id);
        })->count();

        $clinica->mentorias_count = $mentoriasCount;
        $clinica->imagens_count = $imagensCount;

        return response()->json($clinica);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Clinica $clinica)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Clinica $clinica)
    {
        $validated = $request->validate([
            'nome' => 'sometimes|string|max:255',
            'slug' => 'sometimes|string|max:255|unique:clinicas,slug,' . $clinica->id,
            'observacoes' => 'sometimes|string|nullable',
            'endereco_cep' => 'sometimes|string|nullable|max:10',
            'endereco_estado' => 'sometimes|string|nullable|max:2',
            'endereco_cidade' => 'sometimes|string|nullable|max:255',
            'endereco_logradouro' => 'sometimes|string|nullable|max:255',
            'endereco_numero' => 'sometimes|string|nullable|max:20',
            'endereco_bairro' => 'sometimes|string|nullable|max:255',
            'endereco_complemento' => 'sometimes|string|nullable|max:255',
        ]);

        $clinica->update($validated);

        return response()->json([
            'message' => 'Clínica atualizada com sucesso',
            'data' => $clinica
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Clinica $clinica)
    {
        //
    }

    /**
     * Get clinicas with counts for listing
     */
    public function withCounts()
    {
        $clinicas = Clinica::with(['plano'])
            ->withCount(['pacientes', 'dentistas', 'users'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Adicionar contadores de mentorias e informações adicionais para cada clínica
        foreach ($clinicas as $clinica) {
            $clinica->mentorias_count = \App\Models\Mentoria::whereHas('paciente', function($query) use ($clinica) {
                $query->where('clinica_id', $clinica->id);
            })->count();

            // Informações do plano
            if ($clinica->plano) {
                $clinica->plano_nome = $clinica->plano->nome;
                $clinica->plano_cor = $clinica->plano->cor;
                $clinica->plano_valor = $clinica->plano->valor_mensal;
                $clinica->plano_valor_formatado = $clinica->plano->valor_mensal_formatado;
                $clinica->plano_mentorias_max = $clinica->plano->quantidade_mentorias_mensais;
            } else {
                $clinica->plano_nome = 'Sem plano';
                $clinica->plano_cor = '#6c757d';
                $clinica->plano_valor = null;
                $clinica->plano_valor_formatado = 'N/A';
                $clinica->plano_mentorias_max = null;
            }

            // Adicionar status da fidelidade (simulado por enquanto)
            $clinica->status_fidelidade = $this->getStatusFidelidade($clinica);

            // Adicionar status da clínica (simulado por enquanto)
            $clinica->status = $this->getStatusClinica($clinica);
        }

        return response()->json($clinicas);
    }

    /**
     * Determinar status da fidelidade da clínica
     */
    private function getStatusFidelidade($clinica)
    {
        // Por enquanto, vamos simular baseado na data de criação
        $diasDesdeCreacao = now()->diffInDays($clinica->created_at);

        if ($diasDesdeCreacao < 30) {
            return 'Período de teste';
        } elseif ($diasDesdeCreacao < 365) {
            return 'Em fidelidade';
        } else {
            return 'Fidelidade cumprida';
        }
    }

    /**
     * Determinar status da clínica
     */
    private function getStatusClinica($clinica)
    {
        // Por enquanto, vamos simular baseado na atividade recente
        $ultimaAtividade = $clinica->updated_at;
        $diasSemAtividade = now()->diffInDays($ultimaAtividade);

        if ($diasSemAtividade <= 7) {
            return 'Ativo';
        } elseif ($diasSemAtividade <= 30) {
            return 'Pouco ativo';
        } else {
            return 'Inativo';
        }
    }

    /**
     * Search clinicas by name
     */
    public function search(Request $request)
    {
        $search = $request->input('search', '');

        $clinicas = Clinica::with(['plano'])
            ->withCount(['pacientes', 'dentistas', 'users'])
            ->where('nome', 'LIKE', "%{$search}%")
            ->orderBy('created_at', 'desc')
            ->get();

        // Adicionar contadores de mentorias e informações adicionais para cada clínica
        foreach ($clinicas as $clinica) {
            $clinica->mentorias_count = \App\Models\Mentoria::whereHas('paciente', function($query) use ($clinica) {
                $query->where('clinica_id', $clinica->id);
            })->count();

            // Informações do plano
            if ($clinica->plano) {
                $clinica->plano_nome = $clinica->plano->nome;
                $clinica->plano_cor = $clinica->plano->cor;
                $clinica->plano_valor = $clinica->plano->valor_mensal;
                $clinica->plano_valor_formatado = $clinica->plano->valor_mensal_formatado;
                $clinica->plano_mentorias_max = $clinica->plano->quantidade_mentorias_mensais;
            } else {
                $clinica->plano_nome = 'Sem plano';
                $clinica->plano_cor = '#6c757d';
                $clinica->plano_valor = null;
                $clinica->plano_valor_formatado = 'N/A';
                $clinica->plano_mentorias_max = null;
            }

            // Adicionar status da fidelidade
            $clinica->status_fidelidade = $this->getStatusFidelidade($clinica);

            // Adicionar status da clínica
            $clinica->status = $this->getStatusClinica($clinica);
        }

        return response()->json($clinicas);
    }

    /**
     * Generate unique username based on email
     */
    private function generateUniqueUsername($email)
    {
        $baseUsername = explode('@', $email)[0];
        $baseUsername = preg_replace('/[^a-zA-Z0-9]/', '', $baseUsername);

        $username = $baseUsername;
        $counter = 1;

        while (\App\Models\User::where('username', $username)->exists()) {
            $username = $baseUsername . $counter;
            $counter++;
        }

        return $username;
    }
}
